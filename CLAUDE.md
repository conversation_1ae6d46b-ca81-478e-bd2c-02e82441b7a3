# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a **数字工务综合管控平台** (Digital Works Comprehensive Management Platform) built on RuoYi-Vue-Plus v5.3.1. It's an enterprise-level management system designed for digital works administration.

## Technology Stack

- **Framework**: RuoYi-Vue-Plus v5.3.1 (enhanced version of RuoYi-Vue)
- **Language**: Java 17+ (supports JDK 21)
- **Build Tool**: Maven
- **Spring Boot**: 3.4.4
- **Web Container**: Undertow (high-performance alternative to Tomcat)
- **Authentication**: Sa-Token + JWT
- **ORM**: MyBatis-Plus 3.5.11
- **Database**: Multi-database support (MySQL, Oracle, PostgreSQL, SQL Server)
- **Cache**: Redis with Redisson client
- **Documentation**: SpringDoc (OpenAPI 3) with JavaDoc integration
- **Workflow**: Warm-Flow (Chinese workflow engine)
- **Task Scheduling**: SnailJob (distributed job scheduling)

## Build and Development Commands

### Maven Commands
```bash
# Build the entire project
mvn clean package

# Skip tests during build (default behavior)
mvn clean package -DskipTests

# Run specific profile
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# Run tests
mvn test

# Run tests for specific profile
mvn test -Pdev
```

### Application Startup
```bash
# Using provided script
./script/bin/ry.sh start    # Start application
./script/bin/ry.sh stop     # Stop application
./script/bin/ry.sh restart  # Restart application
./script/bin/ry.sh status   # Check status

# Direct Java execution
java -jar ruoyi-admin.jar

# With custom JVM options
java -Xms512m -Xmx1024m -XX:MetaspaceSize=128m -jar ruoyi-admin.jar
```

### Environment Profiles
- **local**: Local development
- **dev**: Development (default profile)
- **prod**: Production

## Project Structure

```
backend/
├── ruoyi-admin/          # Main application entry point
├── ruoyi-common/         # Common modules (23 sub-modules)
│   ├── ruoyi-common-core/        # Core utilities
│   ├── ruoyi-common-web/         # Web configurations
│   ├── ruoyi-common-mybatis/     # Database configurations
│   ├── ruoyi-common-redis/       # Redis configurations
│   ├── ruoyi-common-satoken/     # Authentication
│   ├── ruoyi-common-oss/         # File storage
│   └── ... (other common modules)
├── ruoyi-modules/        # Business modules
│   ├── ruoyi-system/            # System management
│   ├── ruoyi-video/             # Video monitoring
│   ├── ruoyi-floodcontrol/      # Flood control
│   ├── ruoyi-basic/             # Basic functionality
│   ├── ruoyi-workflow/          # Workflow engine
│   ├── ruoyi-generator/         # Code generation
│   ├── ruoyi-job/               # Job scheduling
│   └── ruoyi-demo/              # Demo modules
├── ruoyi-extend/          # Extended functionality
│   ├── ruoyi-monitor-admin/     # Monitoring dashboard
│   └── ruoyi-snailjob-server/   # Job scheduling server
└── script/               # Scripts and configurations
    ├── bin/                     # Startup scripts
    ├── docker/                  # Docker configurations
    └── sql/                     # Database scripts
```

## Key Business Modules

### Core Modules
- **ruoyi-system**: Core system management (users, roles, permissions, departments)
- **ruoyi-video**: Video monitoring and device management
- **ruoyi-floodcontrol**: Flood control management systems
- **ruoyi-basic**: Basic business functionality including emergency response
- **ruoyi-workflow**: Workflow engine integration with visual designer
- **ruoyi-generator**: Code generation tools for CRUD operations

### Infrastructure Modules
- **ruoyi-common-core**: Core utilities and base classes
- **ruoyi-common-web**: Web configurations and filters
- **ruoyi-common-mybatis**: Database and ORM configurations
- **ruoyi-common-redis**: Cache and distributed operations
- **ruoyi-common-satoken**: Authentication and authorization

## Configuration Files

### Main Configuration
- `/ruoyi-admin/src/main/resources/application.yml` - Main configuration
- `/ruoyi-admin/src/main/resources/application-dev.yml` - Development config
- `/ruoyi-admin/src/main/resources/application-prod.yml` - Production config

### Key Configuration Points
- **Server Port**: 8080
- **Database**: MySQL with MyBatis-Plus, multi-datasource support
- **Redis**: Redisson client for caching and distributed operations
- **Authentication**: Sa-Token with JWT
- **File Storage**: Minio/AWS S3 protocol support
- **Multi-tenancy**: Currently disabled (tenant.enable: false)
- **API Documentation**: SpringDoc auto-generated at /swagger-ui/index.html

## Database Setup

### Supported Databases
- MySQL (primary)
- Oracle
- PostgreSQL
- SQL Server

### Database Scripts
- `/script/sql/ry_vue_5.X.sql` - Main database schema
- `/script/sql/ry_workflow.sql` - Workflow tables
- `/script/sql/ry_job.sql` - Job scheduling tables

## Development Workflow

### Environment Setup
1. **Database**: Set up MySQL and run SQL scripts from `/script/sql/`
2. **Redis**: Configure Redis server
3. **JDK**: Install JDK 17+ (JDK 21 supported with virtual threads)
4. **Maven**: Install Maven 3.6+
5. **IDE**: Import as Maven project

### Key Features
- **Hot Reload**: Spring Boot DevTools enabled
- **API Documentation**: Auto-generated via SpringDoc at /swagger-ui/index.html
- **Code Generation**: Built-in generator for CRUD operations
- **Multi-datasource**: Dynamic datasource switching
- **Workflow Engine**: Visual workflow designer at /warm-flow-ui/
- **Monitoring**: Spring Boot Admin dashboard

### Testing
- Tests are located in `/src/test/java/`
- Maven Surefire plugin configured for profile-based test execution
- Tests use JUnit 5 with profile-specific tags
- Default build skips tests (`skipTests=true` in pom.xml)

## Docker Deployment

### Docker Compose
```bash
# Build and run with Docker Compose
docker-compose -f script/docker/docker-compose.yml up -d

# Build Docker image
docker build -t ruoyi/ruoyi-server:5.3.1 .
```

## Important Notes

### Multi-tenancy
- Multi-tenancy is currently disabled in configuration
- When enabled, it supports tenant isolation at database level

### Security
- Sa-Token used for authentication (replaces Spring Security)
- API encryption support with RSA + AES (currently disabled)
- XSS protection enabled
- Data masking and encryption support available

### Performance
- Uses Undertow web container for better performance
- Redisson for Redis operations
- HikariCP for database connection pooling
- ZGC garbage collector configured in startup script

### Development Standards
- Follows Alibaba Java coding conventions
- Uses Lombok for boilerplate code reduction
- MapStruct Plus for object mapping
- Comprehensive JavaDoc for API documentation

## Common Operations

### Adding New Business Module
1. Create new module under `ruoyi-modules/`
2. Add dependency to parent `pom.xml`
3. Create domain, mapper, service, controller layers
4. Use code generator for basic CRUD operations

### Database Changes
1. Use MyBatis-Plus for database operations
2. Follow the established naming conventions
3. Use Snowflake ID generation (ASSIGN_ID)
4. Consider multi-tenancy implications when enabled

### API Development
1. Use SpringDoc annotations for API documentation
2. Follow RESTful conventions
3. Use Sa-Token annotations for authorization
4. Implement proper validation and error handling