package org.dromara.miniofile.common;

/**
 * 公共常量类
 */
public class Constants {

    // 签名值请求头常量
    public static final String SIGNATURE_HEADER = "X-Signature";

    // 设备 ID 请求头常量
    public static final String DEVICE_ID_HEADER = "X-Device-ID";

    // Redis 中签名值缓存的键前缀
    public static final String REDIS_SIGNATURE_KEY_PREFIX = "signature:";

    // Redis 缓存前缀
    public static final String REDIS_CACHE_PREFIX = "miniofile:";
}
