package org.dromara.miniofile.config;

import cn.hutool.crypto.digest.DigestUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.miniofile.common.Constants;
import org.dromara.miniofile.domain.vo.MediaDeviceVo;
import org.dromara.miniofile.service.IMediaDeviceService;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.time.Duration;

@RequiredArgsConstructor
@Component
public class Md5AuthInterceptor implements HandlerInterceptor {


    private final IMediaDeviceService mediaDeviceService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 获取请求头中的签名值
        String signatureHeaderValue = request.getHeader(Constants.SIGNATURE_HEADER);
        if (signatureHeaderValue == null || signatureHeaderValue.isEmpty()) {
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.getWriter().write("Missing Signature header");
            return false;
        }

        // 获取请求头中的设备 ID
        String deviceIdHeaderValue = request.getHeader(Constants.DEVICE_ID_HEADER);
        if (deviceIdHeaderValue == null || deviceIdHeaderValue.isEmpty()) {
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.getWriter().write("Missing Device ID header");
            return false;
        }

        // 生成 Redis 缓存键
        String signatureCacheKey = Constants.REDIS_SIGNATURE_KEY_PREFIX + deviceIdHeaderValue;

        // 从 Redis 获取签名值
        String cachedSignature = RedisUtils.getCacheObject(signatureCacheKey); // 使用 RedisUtils 获取缓存

        if (cachedSignature != null) {
            // 如果 Redis 中存在签名值缓存，则直接验证
            if (!cachedSignature.equals(signatureHeaderValue)) {
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.getWriter().write("Invalid Signature");
                return false;
            }
            return true;
        }

        // 如果 Redis 中不存在签名值缓存，则查询数据库
        MediaDeviceVo mediaDeviceVo = mediaDeviceService.queryById(deviceIdHeaderValue);
        if (mediaDeviceVo == null || mediaDeviceVo.getPassword() == null) {
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.getWriter().write("Invalid Device ID");
            return false;
        }
        String devicePassword = mediaDeviceVo.getPassword();

        // 生成签名值并缓存
        String expectedSignature = DigestUtil.sha256Hex(deviceIdHeaderValue + "::" + devicePassword);
        RedisUtils.setCacheObject(signatureCacheKey, expectedSignature, Duration.ofHours(1)); // 使用 RedisUtils 缓存签名值，设置过期时间为 1 小时

        // 验证签名值
        if (!expectedSignature.equals(signatureHeaderValue)) {
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.getWriter().write("Invalid Signature");
            return false;
        }

        return true;
    }
}
