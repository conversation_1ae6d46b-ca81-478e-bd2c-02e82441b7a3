package org.dromara.miniofile.config;

/**
 * Minio文件上传配置文件
 * @author: jeecg-boot
 */
//@Slf4j
//@Configuration
//@ConditionalOnProperty(prefix = "minio", name = "minio_url")
public class OldMinioConfig {
//    @Value(value = "${minio.minio_url}")
//    private String minioUrl;
//    @Value(value = "${minio.minio_name}")
//    private String minioName;
//    @Value(value = "${minio.minio_pass}")
//    private String minioPass;
//    @Value(value = "${minio.bucketName}")
//    private String bucketName;
//
//    @Bean
//    public Object initMinio(){
//        if(!minioUrl.startsWith("http")){
//            minioUrl = "http://" + minioUrl;
//        }
//        if(!minioUrl.endsWith("/")){
//            minioUrl = minioUrl.concat("/");
//        }
//        MinioUtil.setMinioUrl(minioUrl);
//        MinioUtil.setMinioName(minioName);
//        MinioUtil.setMinioPass(minioPass);
//        MinioUtil.setBucketName(bucketName);
//        return null;
//    }

}
