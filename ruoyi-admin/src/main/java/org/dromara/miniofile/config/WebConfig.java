package org.dromara.miniofile.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    private final Md5AuthInterceptor md5AuthInterceptor;

    public WebConfig(Md5AuthInterceptor md5AuthInterceptor) {
        this.md5AuthInterceptor = md5AuthInterceptor;
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册拦截器，拦截所有请求
        registry.addInterceptor(md5AuthInterceptor).addPathPatterns("/files/**");
    }
}
