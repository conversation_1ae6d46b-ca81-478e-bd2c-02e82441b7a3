package org.dromara.miniofile.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.miniofile.service.IFilesService;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.miniofile.domain.vo.FilesVo;
import org.dromara.miniofile.domain.bo.FilesBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 测试单
 *
 * <AUTHOR> Li
 * @date 2025-04-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/files")
public class FilesController extends BaseController {

    private final IFilesService filesService;

    /**
     * 查询测试单列表
     */
    @SaCheckPermission("web:files:list")
    @GetMapping("/list")
    public TableDataInfo<FilesVo> list(FilesBo bo, PageQuery pageQuery) {
        return filesService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出测试单列表
     */
    @SaCheckPermission("web:files:export")
    @Log(title = "测试单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(FilesBo bo, HttpServletResponse response) {
        List<FilesVo> list = filesService.queryList(bo);
        ExcelUtil.exportExcel(list, "测试单", FilesVo.class, response);
    }

    /**
     * 获取测试单详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("web:files:query")
    @GetMapping("/{id}")
    public R<FilesVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(filesService.queryById(id));
    }

    /**
     * 新增测试单
     */
    @SaCheckPermission("web:files:add")
    @Log(title = "测试单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FilesBo bo) {
        return toAjax(filesService.insertByBo(bo));
    }

    /**
     * 修改测试单
     */
    @SaCheckPermission("web:files:edit")
    @Log(title = "测试单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FilesBo bo) {
        return toAjax(filesService.updateByBo(bo));
    }

    /**
     * 删除测试单
     *
     * @param ids 主键串
     */
    @SaCheckPermission("web:files:remove")
    @Log(title = "测试单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(filesService.deleteWithValidByIds(List.of(ids), true));
    }
}
