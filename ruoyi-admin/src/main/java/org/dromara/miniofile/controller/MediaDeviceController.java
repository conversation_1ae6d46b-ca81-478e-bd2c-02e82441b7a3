//package org.dromara.miniofile.controller;
//
//import java.util.List;
//
//import lombok.RequiredArgsConstructor;
//import jakarta.servlet.http.HttpServletResponse;
//import jakarta.validation.constraints.*;
//import cn.dev33.satoken.annotation.SaCheckPermission;
//import org.springframework.web.bind.annotation.*;
//import org.springframework.validation.annotation.Validated;
//import org.dromara.common.idempotent.annotation.RepeatSubmit;
//import org.dromara.common.log.annotation.Log;
//import org.dromara.common.web.core.BaseController;
//import org.dromara.common.mybatis.core.page.PageQuery;
//import org.dromara.common.core.domain.R;
//import org.dromara.common.core.validate.AddGroup;
//import org.dromara.common.core.validate.EditGroup;
//import org.dromara.common.log.enums.BusinessType;
//import org.dromara.common.excel.utils.ExcelUtil;
//import org.dromara.miniofile.domain.vo.MediaDeviceVo;
//import org.dromara.miniofile.domain.bo.MediaDeviceBo;
//import org.dromara.miniofile.service.IMediaDeviceService;
//import org.dromara.common.mybatis.core.page.TableDataInfo;
//
///**
// * 流媒体设备信息
// *
// * <AUTHOR> Li
// * @date 2025-04-09
// */
//@Validated
//@RequiredArgsConstructor
//@RestController
//@RequestMapping("/miniofile/miniofile")
//public class MediaDeviceController extends BaseController {
//
//    private final IMediaDeviceService mediaDeviceService;
//
//    /**
//     * 查询流媒体设备信息列表
//     */
//    @SaCheckPermission("miniofile:miniofile:list")
//    @GetMapping("/list")
//    public TableDataInfo<MediaDeviceVo> list(MediaDeviceBo bo, PageQuery pageQuery) {
//        return mediaDeviceService.queryPageList(bo, pageQuery);
//    }
//
//    /**
//     * 导出流媒体设备信息列表
//     */
//    @SaCheckPermission("miniofile:miniofile:export")
//    @Log(title = "流媒体设备信息", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(MediaDeviceBo bo, HttpServletResponse response) {
//        List<MediaDeviceVo> list = mediaDeviceService.queryList(bo);
//        ExcelUtil.exportExcel(list, "流媒体设备信息", MediaDeviceVo.class, response);
//    }
//
//    /**
//     * 获取流媒体设备信息详细信息
//     *
//     * @param id 主键
//     */
//    @SaCheckPermission("miniofile:miniofile:query")
//    @GetMapping("/{id}")
//    public R<MediaDeviceVo> getInfo(@NotNull(message = "主键不能为空")
//                                     @PathVariable String id) {
//        return R.ok(mediaDeviceService.queryById(id));
//    }
//
//    /**
//     * 新增流媒体设备信息
//     */
//    @SaCheckPermission("miniofile:miniofile:add")
//    @Log(title = "流媒体设备信息", businessType = BusinessType.INSERT)
//    @RepeatSubmit()
//    @PostMapping()
//    public R<Void> add(@Validated(AddGroup.class) @RequestBody MediaDeviceBo bo) {
//        return toAjax(mediaDeviceService.insertByBo(bo));
//    }
//
//    /**
//     * 修改流媒体设备信息
//     */
//    @SaCheckPermission("miniofile:miniofile:edit")
//    @Log(title = "流媒体设备信息", businessType = BusinessType.UPDATE)
//    @RepeatSubmit()
//    @PutMapping()
//    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MediaDeviceBo bo) {
//        return toAjax(mediaDeviceService.updateByBo(bo));
//    }
//
//    /**
//     * 删除流媒体设备信息
//     *
//     * @param ids 主键串
//     */
//    @SaCheckPermission("miniofile:miniofile:remove")
//    @Log(title = "流媒体设备信息", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{ids}")
//    public R<Void> remove(@NotEmpty(message = "主键不能为空")
//                          @PathVariable String[] ids) {
//        return toAjax(mediaDeviceService.deleteWithValidByIds(List.of(ids), true));
//    }
//}
