package org.dromara.miniofile.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.ServletUtils;
import org.dromara.miniofile.common.Constants;
import org.dromara.miniofile.common.R;
import org.dromara.miniofile.domain.Files;
import org.dromara.miniofile.domain.bo.FileUploadInfoBo;
import org.dromara.miniofile.domain.vo.FileUploadInfoVo;
import org.dromara.miniofile.domain.vo.UploadUrlsVo;
import org.dromara.miniofile.domain.vo.UploadVo;
import org.dromara.miniofile.service.IFilesService;
import org.dromara.miniofile.util.OldMinioUtil;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("/files")
@Slf4j
@SaIgnore
public class MinioController {

    private final IFilesService filesService;

    @RequestMapping("/getMinioUrl")
    public String getMinioUrl() {
        return OldMinioUtil.getMinioUrl();
    }

    /**
     * 检查文件是否存在
     */
    @GetMapping("/multipart/check/{md5}")
    public R<FileUploadInfoVo> checkFileByMd5(@PathVariable String md5) {
        log.info("查询 <{}> 文件是否存在、是否进行断点续传", md5);
        R<FileUploadInfoBo> fileUploadInfoBoR = filesService.checkFileByMd5(md5);
        R<FileUploadInfoVo> fileUploadInfoVoR = new R<>();
        fileUploadInfoVoR.setCode(fileUploadInfoBoR.getCode());
        fileUploadInfoVoR.setMsg(fileUploadInfoBoR.getMsg());
        FileUploadInfoVo fileUploadInfoVo = new FileUploadInfoVo();
        FileUploadInfoBo fileUploadInfoBo = fileUploadInfoBoR.getData();
        if (fileUploadInfoBo != null) {
            fileUploadInfoVo.setMd5(fileUploadInfoBo.getMd5());
            fileUploadInfoVo.setOriginFileName(fileUploadInfoBo.getOriginFileName());
            fileUploadInfoVo.setChunkCount(fileUploadInfoBo.getChunkCount());
            fileUploadInfoVo.setListParts(fileUploadInfoBo.getListParts());
            fileUploadInfoVoR.setData(fileUploadInfoVo);
        }
        return fileUploadInfoVoR;
    }

    /**
     * 初始化文件分片地址及相关数据
     */
    @PostMapping("/multipart/init")
    public R<UploadVo> initMultiPartUpload(@RequestBody FileUploadInfoBo fileUploadInfo) {
        log.info("通过 <{}> 初始化上传任务", fileUploadInfo);
        R<UploadUrlsVo> uploadUrlsVoR = filesService.initMultipartUpload(fileUploadInfo);
        R<UploadVo> uploadVoR = new R<>();
        uploadVoR.setCode(uploadUrlsVoR.getCode());
        uploadVoR.setMsg(uploadUrlsVoR.getMsg());
        UploadUrlsVo uploadUrlsVo = uploadUrlsVoR.getData();
        if (uploadUrlsVo != null) {
            UploadVo uploadVo = new UploadVo();
            uploadVo.setMd5(uploadUrlsVo.getMd5());
            uploadVoR.setData(uploadVo);
        }
        return uploadVoR;
    }

    /**
     * 文件合并（单文件不会合并，仅信息入库）
     */
    @PostMapping("/multipart/merge/{md5}")
    public R<String> mergeMultipartUpload(@PathVariable String md5) {
        log.info("通过 <{}> 合并上传任务", md5);
        // 获取请求头中的设备 ID
        String deviceId = ServletUtils.getRequest().getHeader(Constants.DEVICE_ID_HEADER);
        return filesService.mergeMultipartUpload(md5, deviceId);
    }

    /**
     * 下载文件（分片）
     */
    @GetMapping("/download/{id}")
    public ResponseEntity<byte[]> downloadMultipartFile(@PathVariable Long id, HttpServletRequest request, HttpServletResponse response) throws Exception {
        log.info("通过 <{}> 开始分片下载", id);
        return filesService.downloadMultipartFile(id, request, response);
    }

    @GetMapping("/list")
    public R<List<Files>> getFileList() {
        return filesService.getFileList();
    }

    /**
     * 直接上传分片
     * <p>
     * 通过服务端代理将分片上传到存储服务，避免客户端直接与存储服务通信
     * </p>
     */
    @PostMapping(value ="/multipart/upload/{md5}/{partNumber}", consumes = "multipart/*", headers = "content-type=multipart/form-data", produces = "application/json;charset=UTF-8")
    @Parameters({
        @Parameter(name = "md5", description = "文件唯一标识（MD5值）", in = ParameterIn.PATH, required = true, schema = @Schema(type = "string")),
//        @Parameter(name = "uploadId", description = "minio上传标识", in = ParameterIn.PATH, required = true, schema = @Schema(type = "string")),
        @Parameter(name = "partNumber", description = "分片序号，从1开始", in = ParameterIn.PATH, required = true, schema = @Schema(type = "integer")),
        @Parameter(name = "file", description = "分片文件内容", required = true, schema = @Schema(type = "file"))
    })
    public R<String> uploadPart(@PathVariable("md5") String md5,
                                @PathVariable("partNumber") int partNumber,
                                @RequestPart("file") MultipartFile file) {
        log.info("通过 <uploadId:{},partNumber:{}> 服务上传分片至minio", md5, partNumber);
        try {
            return filesService.uploadPart(md5, partNumber, file.getBytes());
        } catch (IOException e) {
            log.error("上传分片失败", e);
        }
        return R.fail("上传分片失败");
    }

}
