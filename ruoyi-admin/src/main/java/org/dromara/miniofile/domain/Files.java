package org.dromara.miniofile.domain;

import lombok.experimental.Accessors;
import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 测试单对象 files
 *
 * <AUTHOR> Li
 * @date 2025-04-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("files")
public class Files extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 删除标志
     */
    @TableLogic
    private Long delFlag;

    /**
     * 文件上传id
     */
    private String uploadId;

    /**
     * 文件计算md5
     */
    private String md5;

    /**
     * 文件访问地址
     */
    private String url;

    /**
     * 存储桶
     */
    private String bucket;

    /**
     * minio中文件名
     */
    private String object;

    /**
     * 原始文件名
     */
    private String originFileName;

    /**
     * 文件大小
     */
    private Long size;

    /**
     * 文件类型
     */
    private String type;

    /**
     * 分片大小
     */
    private Long chunkSize;

    /**
     * 分片数量
     */
    private Long chunkCount;

    private String deviceId;

    private String deviceName;


}
