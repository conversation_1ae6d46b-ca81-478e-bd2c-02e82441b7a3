package org.dromara.miniofile.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 流媒体设备信息对象 media_device
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@Data
@TableName("media_device")
public class MediaDevice{

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id")
    private String id;

    /**
     * 设备名称
     */
    private String name;

    /**
     * 设备厂家
     */
    private String manufacturer;

    /**
     * 设备型号
     */
    private String model;

    /**
     * 设备固件
     */
    private String firmware;

    /**
     * 传输协议
     */
    private String transport;

    /**
     * 设备视频流类型
     */
    private String streamMode;

    /**
     * 设备访问地址
     */
    private String host;

    /**
     * 设备访问端口
     */
    private Long port;

    /**
     * 设备通道数量
     */
    private Long channelNumber;

    /**
     * 设备描述
     */
    private String description;

    /**
     * 产品ID
     */
    private String productId;

    /**
     * 协议类型,如: gb28181,onvif
     */
    private String provider;

    /**
     * 接入平台的网关ID
     */
    private String gatewayId;

    /**
     * 设备状态
     */
    private String state;

    /**
     * 额外配置
     */
    private String others;

    /**
     * 图片地址
     */
    private String photoUrl;

    /**
     * 接入密码
     */
    private String password;


}
