package org.dromara.miniofile.domain.bo;

import org.dromara.miniofile.domain.Files;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 测试单业务对象 files
 *
 * <AUTHOR>
 * @date 2025-04-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Files.class, reverseConvertGenerate = false)
public class FilesBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 文件上传id
     */
    private String uploadId;

    /**
     * 文件计算md5
     */
    private String md5;

    /**
     * 文件访问地址
     */
    private String url;

    /**
     * 存储桶
     */
    private String bucket;

    /**
     * minio中文件名
     */
    private String object;

    /**
     * 原始文件名
     */
    private String originFileName;

    /**
     * 文件大小
     */
    private Long size;

    /**
     * 文件类型
     */
    private String type;

    /**
     * 分片大小
     */
    private Long chunkSize;

    /**
     * 分片数量
     */
    private Long chunkCount;


}
