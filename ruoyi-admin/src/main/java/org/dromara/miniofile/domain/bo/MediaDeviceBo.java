package org.dromara.miniofile.domain.bo;

import org.dromara.miniofile.domain.MediaDevice;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 流媒体设备信息业务对象 media_device
 *
 * <AUTHOR> Li
 * @date 2025-04-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MediaDevice.class, reverseConvertGenerate = false)
public class MediaDeviceBo extends BaseEntity {

    /**
     * id
     */
    @NotBlank(message = "id不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 设备名称
     */
    private String name;

    /**
     * 设备厂家
     */
    private String manufacturer;

    /**
     * 设备型号
     */
    private String model;

    /**
     * 设备固件
     */
    private String firmware;

    /**
     * 传输协议
     */
    private String transport;

    /**
     * 设备视频流类型
     */
    private String streamMode;

    /**
     * 设备访问地址
     */
    private String host;

    /**
     * 设备访问端口
     */
    private Long port;

    /**
     * 设备通道数量
     */
    private Long channelNumber;

    /**
     * 设备描述
     */
    private String description;

    /**
     * 产品ID
     */
    private String productId;

    /**
     * 协议类型,如: gb28181,onvif
     */
    private String provider;

    /**
     * 接入平台的网关ID
     */
    private String gatewayId;

    /**
     * 设备状态
     */
    private String state;

    /**
     * 额外配置
     */
    private String others;

    /**
     * 图片地址
     */
    private String photoUrl;

    /**
     * 接入密码
     */
    private String password;


}
