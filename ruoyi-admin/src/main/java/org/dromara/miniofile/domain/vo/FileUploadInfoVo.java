package org.dromara.miniofile.domain.vo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class FileUploadInfoVo {

    private String md5;

    private String originFileName;

    private Integer chunkCount;

    private List<Integer> listParts;

}
