package org.dromara.miniofile.domain.vo;

import org.dromara.miniofile.domain.Files;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 测试单视图对象 files
 *
 * <AUTHOR> Li
 * @date 2025-04-01
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Files.class)
public class FilesVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 文件上传id
     */
    @ExcelProperty(value = "文件上传id")
    private String uploadId;

    /**
     * 文件计算md5
     */
    @ExcelProperty(value = "文件计算md5")
    private String md5;

    /**
     * 文件访问地址
     */
    @ExcelProperty(value = "文件访问地址")
    private String url;

    /**
     * 存储桶
     */
    @ExcelProperty(value = "存储桶")
    private String bucket;

    /**
     * minio中文件名
     */
    @ExcelProperty(value = "minio中文件名")
    private String object;

    /**
     * 原始文件名
     */
    @ExcelProperty(value = "原始文件名")
    private String originFileName;

    /**
     * 文件大小
     */
    @ExcelProperty(value = "文件大小")
    private Long size;

    /**
     * 文件类型
     */
    @ExcelProperty(value = "文件类型")
    private String type;

    /**
     * 分片大小
     */
    @ExcelProperty(value = "分片大小")
    private Long chunkSize;

    /**
     * 分片数量
     */
    @ExcelProperty(value = "分片数量")
    private Long chunkCount;


}
