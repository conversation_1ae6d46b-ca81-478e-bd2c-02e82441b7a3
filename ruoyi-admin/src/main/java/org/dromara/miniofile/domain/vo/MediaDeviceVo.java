package org.dromara.miniofile.domain.vo;

import org.dromara.miniofile.domain.MediaDevice;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 流媒体设备信息视图对象 media_device
 *
 * <AUTHOR> Li
 * @date 2025-04-09
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MediaDevice.class)
public class MediaDeviceVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private String id;

    /**
     * 设备名称
     */
    @ExcelProperty(value = "设备名称")
    private String name;

    /**
     * 设备厂家
     */
    @ExcelProperty(value = "设备厂家")
    private String manufacturer;

    /**
     * 设备型号
     */
    @ExcelProperty(value = "设备型号")
    private String model;

    /**
     * 设备固件
     */
    @ExcelProperty(value = "设备固件")
    private String firmware;

    /**
     * 传输协议
     */
    @ExcelProperty(value = "传输协议")
    private String transport;

    /**
     * 设备视频流类型
     */
    @ExcelProperty(value = "设备视频流类型")
    private String streamMode;

    /**
     * 设备访问地址
     */
    @ExcelProperty(value = "设备访问地址")
    private String host;

    /**
     * 设备访问端口
     */
    @ExcelProperty(value = "设备访问端口")
    private Long port;

    /**
     * 设备通道数量
     */
    @ExcelProperty(value = "设备通道数量")
    private Long channelNumber;

    /**
     * 设备描述
     */
    @ExcelProperty(value = "设备描述")
    private String description;

    /**
     * 产品ID
     */
    @ExcelProperty(value = "产品ID")
    private String productId;

    /**
     * 协议类型,如: gb28181,onvif
     */
    @ExcelProperty(value = "协议类型,如: gb28181,onvif")
    private String provider;

    /**
     * 接入平台的网关ID
     */
    @ExcelProperty(value = "接入平台的网关ID")
    private String gatewayId;

    /**
     * 设备状态
     */
    @ExcelProperty(value = "设备状态")
    private String state;

    /**
     * 额外配置
     */
    @ExcelProperty(value = "额外配置")
    private String others;

    /**
     * 图片地址
     */
    @ExcelProperty(value = "图片地址")
    private String photoUrl;

    /**
     * 接入密码
     */
    @ExcelProperty(value = "接入密码")
    private String password;


}
