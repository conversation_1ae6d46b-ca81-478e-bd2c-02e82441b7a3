package org.dromara.miniofile.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import org.dromara.miniofile.domain.MediaDevice;
import org.dromara.miniofile.domain.vo.MediaDeviceVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 流媒体设备信息Mapper接口
 *
 * <AUTHOR> Li
 * @date 2025-04-09
 */
public interface MediaDeviceMapper extends BaseMapperPlus<MediaDevice, MediaDeviceVo> {

}
