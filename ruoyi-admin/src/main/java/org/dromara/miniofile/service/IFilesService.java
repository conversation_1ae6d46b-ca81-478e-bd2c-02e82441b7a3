package org.dromara.miniofile.service;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.dromara.miniofile.common.R;
import org.dromara.miniofile.domain.Files;
import org.dromara.miniofile.domain.bo.FileUploadInfoBo;
import org.dromara.miniofile.domain.vo.FilesVo;
import org.dromara.miniofile.domain.bo.FilesBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.miniofile.domain.vo.UploadUrlsVo;
import org.springframework.http.ResponseEntity;

import java.util.Collection;
import java.util.List;

/**
 * 测试单Service接口
 *
 * <AUTHOR> Li
 * @date 2025-04-01
 */
public interface IFilesService {

    /**
     * 查询测试单
     *
     * @param id 主键
     * @return 测试单
     */
    FilesVo queryById(Long id);

    /**
     * 分页查询测试单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 测试单分页列表
     */
    TableDataInfo<FilesVo> queryPageList(FilesBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的测试单列表
     *
     * @param bo 查询条件
     * @return 测试单列表
     */
    List<FilesVo> queryList(FilesBo bo);

    /**
     * 新增测试单
     *
     * @param bo 测试单
     * @return 是否新增成功
     */
    Boolean insertByBo(FilesBo bo);

    /**
     * 修改测试单
     *
     * @param bo 测试单
     * @return 是否修改成功
     */
    Boolean updateByBo(FilesBo bo);

    /**
     * 校验并批量删除测试单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    R<FileUploadInfoBo> checkFileByMd5(String md5);

    R<UploadUrlsVo> initMultipartUpload(FileUploadInfoBo fileUploadInfo);

    R<String> mergeMultipartUpload(String md5, String deviceId);

    ResponseEntity<byte[]> downloadMultipartFile(Long id, HttpServletRequest request, HttpServletResponse response) throws Exception;

    R<List<Files>> getFileList();

    R<String> uploadPart(String md5, int partNumber, byte[] fileBytes);

}
