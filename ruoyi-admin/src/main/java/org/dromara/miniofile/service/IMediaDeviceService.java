package org.dromara.miniofile.service;

import org.dromara.miniofile.domain.vo.MediaDeviceVo;
import org.dromara.miniofile.domain.bo.MediaDeviceBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 流媒体设备信息Service接口
 *
 * <AUTHOR> Li
 * @date 2025-04-09
 */
public interface IMediaDeviceService {

    /**
     * 查询流媒体设备信息
     *
     * @param id 主键
     * @return 流媒体设备信息
     */
    MediaDeviceVo queryById(String id);

    /**
     * 分页查询流媒体设备信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 流媒体设备信息分页列表
     */
    TableDataInfo<MediaDeviceVo> queryPageList(MediaDeviceBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的流媒体设备信息列表
     *
     * @param bo 查询条件
     * @return 流媒体设备信息列表
     */
    List<MediaDeviceVo> queryList(MediaDeviceBo bo);

    /**
     * 新增流媒体设备信息
     *
     * @param bo 流媒体设备信息
     * @return 是否新增成功
     */
    Boolean insertByBo(MediaDeviceBo bo);

    /**
     * 修改流媒体设备信息
     *
     * @param bo 流媒体设备信息
     * @return 是否修改成功
     */
    Boolean updateByBo(MediaDeviceBo bo);

    /**
     * 校验并批量删除流媒体设备信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
