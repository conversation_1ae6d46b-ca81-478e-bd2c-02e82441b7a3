package org.dromara.miniofile.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import io.minio.GetObjectResponse;
import io.minio.StatObjectResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.miniofile.common.Constants;
import org.dromara.miniofile.common.R;
import org.dromara.miniofile.domain.bo.FileUploadInfoBo;
import org.dromara.miniofile.domain.vo.MediaDeviceVo;
import org.dromara.miniofile.domain.vo.UploadUrlsVo;
import org.dromara.miniofile.domain.Files;
import org.dromara.miniofile.domain.bo.FilesBo;
import org.dromara.miniofile.domain.vo.FilesVo;
import org.dromara.miniofile.enums.HttpCodeEnum;
import org.dromara.miniofile.mapper.FilesMapper;
import org.dromara.miniofile.service.IFilesService;
import org.dromara.miniofile.service.IMediaDeviceService;
import org.dromara.miniofile.util.MinioUtil;
import org.dromara.miniofile.config.MinioConfigInfo;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.BufferedOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 测试单Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-04-01
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class FilesServiceImpl implements IFilesService {

    private final FilesMapper baseMapper;

    private static final Integer BUFFER_SIZE = 1024 * 64; // 64KB

    private final MinioUtil minioUtil;

    private final MinioConfigInfo minioConfigInfo;

    private static final String KEY_PREFIX = Constants.REDIS_CACHE_PREFIX;

    private final IMediaDeviceService mediaDeviceService;

    /**
     * 查询测试单
     *
     * @param id 主键
     * @return 测试单
     */
    @Override
    public FilesVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询测试单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 测试单分页列表
     */
    @Override
    public TableDataInfo<FilesVo> queryPageList(FilesBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Files> lqw = buildQueryWrapper(bo);
        Page<FilesVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的测试单列表
     *
     * @param bo 查询条件
     * @return 测试单列表
     */
    @Override
    public List<FilesVo> queryList(FilesBo bo) {
        LambdaQueryWrapper<Files> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Files> buildQueryWrapper(FilesBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Files> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getUploadId()), Files::getUploadId, bo.getUploadId());
        lqw.eq(StringUtils.isNotBlank(bo.getMd5()), Files::getMd5, bo.getMd5());
        lqw.eq(StringUtils.isNotBlank(bo.getUrl()), Files::getUrl, bo.getUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getBucket()), Files::getBucket, bo.getBucket());
        lqw.eq(StringUtils.isNotBlank(bo.getObject()), Files::getObject, bo.getObject());
        lqw.like(StringUtils.isNotBlank(bo.getOriginFileName()), Files::getOriginFileName, bo.getOriginFileName());
        lqw.eq(bo.getSize() != null, Files::getSize, bo.getSize());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), Files::getType, bo.getType());
        lqw.eq(bo.getChunkSize() != null, Files::getChunkSize, bo.getChunkSize());
        lqw.eq(bo.getChunkCount() != null, Files::getChunkCount, bo.getChunkCount());
        return lqw;
    }

    /**
     * 新增测试单
     *
     * @param bo 测试单
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(FilesBo bo) {
        Files add = MapstructUtils.convert(bo, Files.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改测试单
     *
     * @param bo 测试单
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(FilesBo bo) {
        Files update = MapstructUtils.convert(bo, Files.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Files entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除测试单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public R<FileUploadInfoBo> checkFileByMd5(String md5) {
        log.info("查询md5: <{}> 在redis是否存在", md5);
        FileUploadInfoBo fileUploadInfo = (FileUploadInfoBo)RedisUtils.getCacheObject(StrUtil.concat(true, KEY_PREFIX, md5));
        if (fileUploadInfo != null) {
            List<Integer> listParts = minioUtil.getListParts(fileUploadInfo.getObject(), fileUploadInfo.getUploadId());
            fileUploadInfo.setListParts(listParts);
            return R.http(HttpCodeEnum.UPLOADING, fileUploadInfo);
        }
        log.info("redis中不存在md5: <{}> 查询mysql是否存在", md5);
        // 注释秒传功能
//        Files file = baseMapper.selectOne(new LambdaQueryWrapper<Files>().eq(Files::getMd5, md5));
//        if (file != null) {
//            log.info("mysql中存在md5: <{}> 的文件 该文件已上传至minio 秒传直接过", md5);
//            FileUploadInfoBo dbFileInfo = BeanUtil.copyProperties(file, FileUploadInfoBo.class);
//            return R.http(HttpCodeEnum.UPLOAD_SUCCESS, dbFileInfo);
//        }

        return R.http(HttpCodeEnum.NOT_UPLOADED, null);
    }

    @Override
    public R<UploadUrlsVo> initMultipartUpload(FileUploadInfoBo fileUploadInfo) {
        FileUploadInfoBo redisFileUploadInfo = (FileUploadInfoBo)RedisUtils.getCacheObject(StrUtil.concat(true, KEY_PREFIX, fileUploadInfo.getMd5()));
        // 若 redis 中有该 md5 的记录，以 redis 中为主
        String object;
        if (redisFileUploadInfo != null) {
            fileUploadInfo = redisFileUploadInfo;
            object = redisFileUploadInfo.getObject();
        } else {
            String originFileName = fileUploadInfo.getOriginFileName();
            String suffix = FileUtil.extName(originFileName);
            String fileName = FileUtil.mainName(originFileName);
            // 对文件重命名，并以年月日文件夹格式存储
            String nestFile = DateUtil.format(LocalDateTime.now(), "yyyy/MM/dd");
            object = nestFile + "/" + fileName + "_" + fileUploadInfo.getMd5() + "." + suffix;

            fileUploadInfo.setObject(object).setType(suffix);
        }

        UploadUrlsVo urlsVo;
        // 单文件上传
        if (fileUploadInfo.getChunkCount() == 1) {
            log.info("当前分片数量 <{}> 单文件上传", fileUploadInfo.getChunkCount());
            urlsVo = minioUtil.getUploadObjectUrl(fileUploadInfo.getContentType(), object);
        } else {
            // 分片上传
            log.info("当前分片数量 <{}> 分片上传", fileUploadInfo.getChunkCount());
            urlsVo = minioUtil.initMultiPartUpload(fileUploadInfo, object);
        }
        fileUploadInfo.setUploadId(urlsVo.getUploadId());
        urlsVo.setMd5(fileUploadInfo.getMd5());

        // 存入 redis （单片存 redis 唯一用处就是可以让单片也入库，因为单片只有一个请求，基本不会出现问题）
        RedisUtils.setCacheObject(StrUtil.concat(true, KEY_PREFIX, fileUploadInfo.getMd5()), fileUploadInfo, Duration.ofDays(minioConfigInfo.getBreakpointTime()));
        return R.ok(urlsVo);
    }

    @Override
    public R<String> mergeMultipartUpload(String md5, String deviceId) {
        FileUploadInfoBo redisFileUploadInfo = (FileUploadInfoBo)RedisUtils.getCacheObject(StrUtil.concat(true, KEY_PREFIX, md5));

        String url = StrUtil.format("{}/{}/{}", minioConfigInfo.getEndpoint(), minioConfigInfo.getBucket(), redisFileUploadInfo.getObject());
        Files files = BeanUtil.copyProperties(redisFileUploadInfo, Files.class);
        files.setUrl(url)
            .setBucket(minioConfigInfo.getBucket());

        Integer chunkCount = redisFileUploadInfo.getChunkCount();
        // 分片为 1 ，不需要合并，否则合并后看返回的是 true 还是 false
        boolean isSuccess = chunkCount == 1 || minioUtil.mergeMultipartUpload(redisFileUploadInfo.getObject(), redisFileUploadInfo.getUploadId());
        if (isSuccess) {
            if (StringUtils.isNotBlank(deviceId)) {
                // 查询设备信息
                MediaDeviceVo mediaDeviceVo = mediaDeviceService.queryById(deviceId);
                if (mediaDeviceVo != null) {
                    // 填充设备信息到 files 对象
                    files.setDeviceId(mediaDeviceVo.getId());
                    files.setDeviceName(mediaDeviceVo.getName());
                }
            }
            baseMapper.insert(files);
            RedisUtils.deleteObject(StrUtil.concat(true, KEY_PREFIX, md5));
            return R.http(HttpCodeEnum.SUCCESS, url);
        }
        return R.http(HttpCodeEnum.UPLOAD_FILE_FAILED, null);
    }

    @Override
    public ResponseEntity<byte[]> downloadMultipartFile(Long id, HttpServletRequest request, HttpServletResponse response) throws IOException {
        // redis 缓存当前文件信息，避免分片下载时频繁查库
        Files file = null;
        Files redisFile = (Files)RedisUtils.getCacheObject(StrUtil.concat(true, KEY_PREFIX, String.valueOf(id)));
        if (redisFile == null) {
            Files dbFile = baseMapper.selectById(id);
            if (dbFile == null) {
                return null;
            } else {
                file = dbFile;
                RedisUtils.setCacheObject(StrUtil.concat(true, KEY_PREFIX, String.valueOf(id)), file, Duration.ofDays(1));
            }
        } else {
            file = redisFile;
        }

        String range = request.getHeader("Range");
        String fileName = file.getOriginFileName();
        log.info("下载文件的 object <{}>", file.getObject());
        // 获取 bucket 桶中的文件元信息，获取不到会抛出异常
        StatObjectResponse objectResponse = minioUtil.statObject(file.getObject());
        long startByte = 0; // 开始下载位置
        long fileSize = objectResponse.size();
        long endByte = fileSize - 1; // 结束下载位置
        log.info("文件总长度：{}，当前 range：{}", fileSize, range);

        BufferedOutputStream os = null; // buffer 写入流
        GetObjectResponse stream = null; // minio 文件流

        // 存在 range，需要根据前端下载长度进行下载，即分段下载
        // 例如：range=bytes=0-52428800
        if (range != null && range.contains("bytes=") && range.contains("-")) {
            range = range.substring(range.lastIndexOf("=") + 1).trim(); // 0-52428800
            String[] ranges = range.split("-");
            // 判断range的类型
            if (ranges.length == 1) {
                // 类型一：bytes=-2343 后端转换为 0-2343
                if (range.startsWith("-")) endByte = Long.parseLong(ranges[0]);
                // 类型二：bytes=2343- 后端转换为 2343-最后
                if (range.endsWith("-")) startByte = Long.parseLong(ranges[0]);
            } else if (ranges.length == 2) { // 类型三：bytes=22-2343
                startByte = Long.parseLong(ranges[0]);
                endByte = Long.parseLong(ranges[1]);
            }
        }

        // 要下载的长度
        // 确保返回的 contentLength 不会超过文件的实际剩余大小
        long contentLength = Math.min(endByte - startByte + 1, fileSize - startByte);
        // 文件类型
        String contentType = request.getServletContext().getMimeType(fileName);

        // 解决下载文件时文件名乱码问题
        byte[] fileNameBytes = fileName.getBytes(StandardCharsets.UTF_8);
        fileName = new String(fileNameBytes, 0, fileNameBytes.length, StandardCharsets.ISO_8859_1);

        // 响应头设置---------------------------------------------------------------------------------------------
        // 断点续传，获取部分字节内容：
        response.setHeader("Accept-Ranges", "bytes");
        // http状态码要为206：表示获取部分内容,SC_PARTIAL_CONTENT,若部分浏览器不支持，改成 SC_OK
        response.setStatus(HttpServletResponse.SC_PARTIAL_CONTENT);
        response.setContentType(contentType);
        response.setHeader("Last-Modified", objectResponse.lastModified().toString());
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        response.setHeader("Content-Length", String.valueOf(contentLength));
        // Content-Range，格式为：[要下载的开始位置]-[结束位置]/[文件总大小]
        response.setHeader("Content-Range", "bytes " + startByte + "-" + endByte + "/" + objectResponse.size());
        response.setHeader("ETag", "\"".concat(objectResponse.etag()).concat("\""));
        response.setContentType("application/octet-stream;charset=UTF-8");

        try {
            // 获取文件流
            stream = minioUtil.getObject(objectResponse.object(), startByte, contentLength);
            os = new BufferedOutputStream(response.getOutputStream());
            // 将读取的文件写入到 OutputStream
            byte[] bytes = new byte[BUFFER_SIZE];
            long bytesWritten = 0;
            int bytesRead = -1;
            while ((bytesRead = stream.read(bytes)) != -1) {
                if (bytesWritten + bytesRead >= contentLength) {
                    os.write(bytes, 0, (int)(contentLength - bytesWritten));
                    break;
                } else {
                    os.write(bytes, 0, bytesRead);
                    bytesWritten += bytesRead;
                }
            }
            os.flush();
            response.flushBuffer();
            // 返回对应http状态
            return new ResponseEntity<>(bytes, HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (os != null) os.close();
            if (stream != null) stream.close();
        }
        return null;
    }

    @Override
    public R<List<Files>> getFileList() {
        List<Files> filesList = baseMapper.selectList(null);
        return R.ok(filesList);
    }

    @Override
    public R<String> uploadPart(String md5, int partNumber, byte[] fileBytes) {
        FileUploadInfoBo redisFileUploadInfo = (FileUploadInfoBo)RedisUtils.getCacheObject(StrUtil.concat(true, KEY_PREFIX, md5));
        if(redisFileUploadInfo == null){
            return R.fail("未找到分片信息");
        }
        boolean uploadFlag = false;
        if(redisFileUploadInfo.getChunkCount() == 1){
            // 单文件上传
            uploadFlag = minioUtil.putObject(redisFileUploadInfo.getObject(), fileBytes);
        }else{
            uploadFlag = minioUtil.uploadPart(redisFileUploadInfo.getObject(), redisFileUploadInfo.getUploadId(), partNumber, fileBytes);
        }
        if(uploadFlag){
            return R.ok("上传分片成功");
        }else{
            return R.fail("上传分片失败");
        }
    }
}
