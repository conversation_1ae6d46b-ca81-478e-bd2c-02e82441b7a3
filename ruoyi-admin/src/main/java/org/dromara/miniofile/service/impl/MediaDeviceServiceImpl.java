package org.dromara.miniofile.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.miniofile.domain.bo.MediaDeviceBo;
import org.dromara.miniofile.domain.vo.MediaDeviceVo;
import org.dromara.miniofile.domain.MediaDevice;
import org.dromara.miniofile.mapper.MediaDeviceMapper;
import org.dromara.miniofile.service.IMediaDeviceService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 流媒体设备信息Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-04-09
 */
@DS("iot")
@RequiredArgsConstructor
@Service
public class MediaDeviceServiceImpl implements IMediaDeviceService {

    private final MediaDeviceMapper baseMapper;

    /**
     * 查询流媒体设备信息
     *
     * @param id 主键
     * @return 流媒体设备信息
     */
    @Override
    public MediaDeviceVo queryById(String id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询流媒体设备信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 流媒体设备信息分页列表
     */
    @Override
    public TableDataInfo<MediaDeviceVo> queryPageList(MediaDeviceBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MediaDevice> lqw = buildQueryWrapper(bo);
        Page<MediaDeviceVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的流媒体设备信息列表
     *
     * @param bo 查询条件
     * @return 流媒体设备信息列表
     */
    @Override
    public List<MediaDeviceVo> queryList(MediaDeviceBo bo) {
        LambdaQueryWrapper<MediaDevice> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MediaDevice> buildQueryWrapper(MediaDeviceBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MediaDevice> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(MediaDevice::getId);
        lqw.like(StringUtils.isNotBlank(bo.getName()), MediaDevice::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getManufacturer()), MediaDevice::getManufacturer, bo.getManufacturer());
        lqw.eq(StringUtils.isNotBlank(bo.getModel()), MediaDevice::getModel, bo.getModel());
        lqw.eq(StringUtils.isNotBlank(bo.getFirmware()), MediaDevice::getFirmware, bo.getFirmware());
        lqw.eq(StringUtils.isNotBlank(bo.getTransport()), MediaDevice::getTransport, bo.getTransport());
        lqw.eq(StringUtils.isNotBlank(bo.getStreamMode()), MediaDevice::getStreamMode, bo.getStreamMode());
        lqw.eq(StringUtils.isNotBlank(bo.getHost()), MediaDevice::getHost, bo.getHost());
        lqw.eq(bo.getPort() != null, MediaDevice::getPort, bo.getPort());
        lqw.eq(bo.getChannelNumber() != null, MediaDevice::getChannelNumber, bo.getChannelNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getDescription()), MediaDevice::getDescription, bo.getDescription());
        lqw.eq(StringUtils.isNotBlank(bo.getProductId()), MediaDevice::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProvider()), MediaDevice::getProvider, bo.getProvider());
        lqw.eq(StringUtils.isNotBlank(bo.getGatewayId()), MediaDevice::getGatewayId, bo.getGatewayId());
        lqw.eq(StringUtils.isNotBlank(bo.getState()), MediaDevice::getState, bo.getState());
        lqw.eq(StringUtils.isNotBlank(bo.getOthers()), MediaDevice::getOthers, bo.getOthers());
        lqw.eq(StringUtils.isNotBlank(bo.getPhotoUrl()), MediaDevice::getPhotoUrl, bo.getPhotoUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getPassword()), MediaDevice::getPassword, bo.getPassword());
        return lqw;
    }

    /**
     * 新增流媒体设备信息
     *
     * @param bo 流媒体设备信息
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MediaDeviceBo bo) {
        MediaDevice add = MapstructUtils.convert(bo, MediaDevice.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改流媒体设备信息
     *
     * @param bo 流媒体设备信息
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MediaDeviceBo bo) {
        MediaDevice update = MapstructUtils.convert(bo, MediaDevice.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MediaDevice entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除流媒体设备信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
