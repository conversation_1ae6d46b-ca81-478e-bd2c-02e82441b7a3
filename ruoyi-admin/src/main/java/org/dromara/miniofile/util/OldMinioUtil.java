package org.dromara.miniofile.util;

import cn.hutool.core.util.StrUtil;
import io.minio.*;
import io.minio.http.Method;
import lombok.extern.slf4j.Slf4j;
import org.anyline.util.FileUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.net.URLDecoder;

/**
 * minio文件上传工具类
 * @author: jeecg-boot
 */
@Slf4j
public class OldMinioUtil {
    private static String minioUrl;
    private static String minioName;
    private static String minioPass;
    private static String bucketName;

    public static void setMinioUrl(String minioUrl) {
        OldMinioUtil.minioUrl = minioUrl;
    }

    public static void setMinioName(String minioName) {
        OldMinioUtil.minioName = minioName;
    }

    public static void setMinioPass(String minioPass) {
        OldMinioUtil.minioPass = minioPass;
    }

    public static void setBucketName(String bucketName) {
        OldMinioUtil.bucketName = bucketName;
    }

    public static String getMinioUrl() {
        return minioUrl;
    }

    public static String getBucketName() {
        return bucketName;
    }

    private static MinioClient minioClient = null;

    /**
     * 上传文件
     * @param file
     * @return
     */
    public static String upload(MultipartFile file, String bizPath, String customBucket) throws Exception {
        String fileUrl = "";

        String newBucket = bucketName;
        if(StrUtil.isNotEmpty(customBucket)){
            newBucket = customBucket;
        }
        try {
            initMinio(minioUrl, minioName,minioPass);
            // 检查存储桶是否已经存在
            if(minioClient.bucketExists(BucketExistsArgs.builder().bucket(newBucket).build())) {
                log.info("Bucket already exists.");
            } else {
                // 创建一个名为ota的存储桶
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(newBucket).build());
                log.info("create a new bucket.");
            }
            InputStream stream = file.getInputStream();
            // 获取文件名
            String orgName = file.getOriginalFilename();
            if("".equals(orgName)){
                orgName=file.getName();
            }
            orgName = FileUtil.getFileName(orgName);
            String objectName = bizPath+"/"
                                +( orgName.indexOf(".")==-1
                                   ?orgName + "_" + System.currentTimeMillis()
                                   :orgName.substring(0, orgName.lastIndexOf(".")) + "_" + System.currentTimeMillis() + orgName.substring(orgName.lastIndexOf("."))
                                 );

            // 使用putObject上传一个本地文件到存储桶中。
            if(objectName.startsWith("/")){
                objectName = objectName.substring(1);
            }
            PutObjectArgs objectArgs = PutObjectArgs.builder().object(objectName)
                    .bucket(newBucket)
                    .contentType("application/octet-stream")
                    .stream(stream,stream.available(),-1).build();
            minioClient.putObject(objectArgs);
            stream.close();
            fileUrl = minioUrl+newBucket+"/"+objectName;
        }catch (Exception e){
            log.error(e.getMessage(), e);
        }
        return fileUrl;
    }

    /**
     * 文件上传
     * @param file
     * @param bizPath
     * @return
     */
    public static String upload(MultipartFile file, String bizPath) throws Exception {
        return upload(file,bizPath,null);
    }

    /**
     * 获取文件流
     * @param bucketName
     * @param objectName
     * @return
     */
    public static InputStream getMinioFile(String bucketName,String objectName){
        InputStream inputStream = null;
        try {
            initMinio(minioUrl, minioName, minioPass);
            GetObjectArgs objectArgs = GetObjectArgs.builder().object(objectName)
                    .bucket(bucketName).build();
            inputStream = minioClient.getObject(objectArgs);
        } catch (Exception e) {
            log.info("文件获取失败" + e.getMessage());
        }
        return inputStream;
    }

    /**
     * 删除文件
     * @param bucketName
     * @param objectName
     * @throws Exception
     */
    public static void removeObject(String bucketName, String objectName) {
        try {
            initMinio(minioUrl, minioName,minioPass);
            RemoveObjectArgs objectArgs = RemoveObjectArgs.builder().object(objectName)
                    .bucket(bucketName).build();
            minioClient.removeObject(objectArgs);
        }catch (Exception e){
            log.info("文件删除失败" + e.getMessage());
        }
    }

    /**
     * 获取文件外链
     * @param bucketName
     * @param objectName
     * @param expires
     * @return
     */
    public static String getObjectUrl(String bucketName, String objectName, Integer expires) {
        initMinio(minioUrl, minioName,minioPass);
        try{
            //update-begin---author:liusq  Date:20220121  for：获取文件外链报错提示method不能为空，导致文件下载和预览失败----
            GetPresignedObjectUrlArgs objectArgs = GetPresignedObjectUrlArgs.builder().object(objectName)
                    .bucket(bucketName)
                    .expiry(expires).method(Method.GET).build();
            //update-begin---author:liusq  Date:20220121  for：获取文件外链报错提示method不能为空，导致文件下载和预览失败----
            String url = minioClient.getPresignedObjectUrl(objectArgs);
            return URLDecoder.decode(url,"UTF-8");
        }catch (Exception e){
            log.info("文件路径获取失败" + e.getMessage());
        }
        return null;
    }

    /**
     * 初始化客户端
     * @param minioUrl
     * @param minioName
     * @param minioPass
     * @return
     */
    private static MinioClient initMinio(String minioUrl, String minioName,String minioPass) {
        if (minioClient == null) {
            try {
                minioClient = MinioClient.builder()
                        .endpoint(minioUrl)
                        .credentials(minioName, minioPass)
                        .build();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return minioClient;
    }

    /**
     * 上传文件到minio
     * @param stream
     * @param relativePath
     * @return
     */
    public static String upload(InputStream stream,String relativePath) throws Exception {
        initMinio(minioUrl, minioName,minioPass);
        if(minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build())) {
            log.info("Bucket already exists.");
        } else {
            // 创建一个名为ota的存储桶
            minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
            log.info("create a new bucket.");
        }
        PutObjectArgs objectArgs = PutObjectArgs.builder().object(relativePath)
                .bucket(bucketName)
                .contentType("application/octet-stream")
                .stream(stream,stream.available(),-1).build();
        minioClient.putObject(objectArgs);
        stream.close();
        return minioUrl+bucketName+"/"+relativePath;
    }

    /**
     * 获取文件带token的url
     * @param url
     * @return
     */
    public static String getMinioUrlTakeToken(String url) {

    try {
        String minioFilePrefix = OldMinioUtil.getMinioUrl() + OldMinioUtil.getBucketName() + "/";
        String objectName = StringUtils.remove(url, minioFilePrefix);

        // 初始化MinIO客户端
        initMinio(minioUrl, minioName, minioPass);

        // 获取预签名URL
        GetPresignedObjectUrlArgs objectArgs = GetPresignedObjectUrlArgs.builder()
                .object(objectName)
                .bucket(bucketName)
                .expiry(60) // 设置过期时间为60
                .method(Method.GET)
                .build();

        String presignedUrl = minioClient.getPresignedObjectUrl(objectArgs);
        return URLDecoder.decode(presignedUrl, "UTF-8");
    } catch (Exception e) {
        log.error("获取带token的URL失败: " + e.getMessage(), e);
    }
    return url;

    }

//
//    /**
//     * 合并分片文件
//     * @param objectName  对象名 例：/temp/ppt/5c7e4b370488494986c2f9284ea6eab9-A001.ppt
//     * @return ObjectWriteResponse
//     */
//    @SneakyThrows
//    public static void mergeFile(String objectName) {
//        // 将相对路径文件名拆分成数组
//        String[] split = objectName.split("/");
//        // 将相对路径文件名去除文件UUID名称 和 路径第一个字符 / -> temp/ppt/
//        String parentPath = objectName.replaceAll(split[split.length - 1], "").substring(1);
//        // 获取文件名称中的UUID -> 5c7e4b370488494986c2f9284ea6eab9
//        String uuid = split[split.length - 1].split("-")[0];
//        // 拼接分片文件存储的相对路径 -> temp/ppt/5c7e4b370488494986c2f9284ea6eab9/
//        String prefix = parentPath + uuid + "/";
//        // 获取所有分片对象
//        Iterable<Result<Item>> parts = minioClient.listObjects(
//            ListObjectsArgs.builder()
//                .bucket(bucketName)
//                .prefix(prefix)  // 将分片文件存储的相对路径下的所有文件查出来
//                .recursive(false)
//                .build());
//        // 将所有分片对象存储在一个列表中以便后续操作
//        List<Item> partList = new ArrayList<>();
//        for (Result<Item> result : parts) {
//            Item item = result.get();
//            if (!item.isDir()) {
//                partList.add(item);
//            }
//        }
//        // 按分片编号排序
//        Collections.sort(partList, Comparator.comparingInt(o -> extractPartNumber(o.objectName())));
//        //获取需要合并的分片组装成ComposeSource
//        List<ComposeSource> sourceObjectList = new ArrayList<>(partList.size());
//        for (Item object : partList) {
//            // 每个分片的相对路径文件名称
//            String partName = object.objectName();
//            sourceObjectList.add(
//                ComposeSource.builder()
//                    .bucket(bucketName)
//                    .object(partName)
//                    .build()
//            );
//        }
//        //合并分片
//        try {
//            ComposeObjectArgs composeObjectArgs = ComposeObjectArgs.builder()
//                .bucket(bucketName)
//                //合并后的相对路径文件名称
//                .object(objectName)
//                //指定源文件
//                .sources(sourceObjectList)
//                .build();
//            // 开始合并
//            minioClient.composeObject(composeObjectArgs);
//        } catch (Exception e) {
//            System.err.println("Error merge object " + objectName + ": " + e.getMessage());
//            throw e;
//        }
//        // 删除所有分片对象，保存分片的临时UUID目录也会因为分片文件删除后自动清除
//        for (Item part : partList) {
//            String partName = part.objectName();
//            try {
//                // 删除所有临时存储的分片文件
//                minioClient.removeObject(RemoveObjectArgs.builder().bucket(bucketName).object(partName).build());
//            } catch (Exception e) {
//                System.err.println("Error removing object " + partName + ": " + e.getMessage());
//                throw e;
//            }
//        }
//    }
//
//    private static int extractPartNumber(String objectName) {
//        // 假设分片文件名格式为 UUID-分片编号.扩展名
//        // 例如：5c7e4b370488494986c2f9284ea6eab9-001.part
//        String[] parts = objectName.split("-");
//        if (parts.length < 2) {
//            throw new IllegalArgumentException("Invalid object name format: " + objectName);
//        }
//        String partNumberStr = parts[1].split("\\.")[0]; // 获取分片编号部分并去掉扩展名
//        try {
//            return Integer.parseInt(partNumberStr);
//        } catch (NumberFormatException e) {
//            throw new IllegalArgumentException("Invalid part number in object name: " + objectName, e);
//        }
//    }

}
