package org.dromara.web.service.impl;

import cn.dev33.satoken.secure.BCrypt;
import cn.dev33.satoken.stp.SaLoginModel;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.constant.Constants;
import org.dromara.common.core.constant.GlobalConstants;
import org.dromara.common.core.constant.SystemConstants;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.domain.model.PasswordLoginBody;
import org.dromara.common.core.enums.LoginType;
import org.dromara.common.core.exception.user.CaptchaException;
import org.dromara.common.core.exception.user.CaptchaExpireException;
import org.dromara.common.core.exception.user.UserException;
import org.dromara.common.core.utils.MessageUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.core.utils.ValidatorUtils;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.common.web.config.properties.CaptchaProperties;
import org.dromara.system.domain.SysUser;
import org.dromara.system.domain.vo.SysClientVo;
import org.dromara.system.domain.vo.SysUserVo;
import org.dromara.system.domain.bo.SysUserBo;
import org.dromara.system.mapper.SysUserMapper;
import org.dromara.system.service.ISysUserService;
import org.dromara.web.domain.vo.LoginVo;
import org.dromara.web.service.IAuthStrategy;
import org.dromara.web.service.SysLoginService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 密码认证策略
 *
 * <AUTHOR>
 */
@Slf4j
@Service("password" + IAuthStrategy.BASE_NAME)
@RequiredArgsConstructor
public class PasswordAuthStrategy implements IAuthStrategy {

    private final CaptchaProperties captchaProperties;
    private final SysLoginService loginService;
    private final SysUserMapper userMapper;
    private final ISysUserService userService;

    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    @Value("${auth.unified-auth-url}")
    private String unifiedAuthUrl;

    @Value("${auth.aes-key:123456789abcdefg}")
    private String aesKey;

    @Value("${auth.allow-local-auth:false}")
    private boolean allowLocalAuth;

//    @Override
//    public LoginVo login(String body, SysClientVo client) {
//        // 在生产环境下不允许本地用户名密码登录
//        if ("prod".equals(activeProfile)) {
//            log.warn("生产环境下不允许使用本地用户名密码登录，请使用统一身份认证。");
//            throw new UserException("生产环境下不允许使用本地用户名密码登录，请使用统一身份认证。");
//        }
//
//        log.info("开发环境下允许本地用户名密码登录: profile={}", activeProfile);
//
//        PasswordLoginBody loginBody = JsonUtils.parseObject(body, PasswordLoginBody.class);
//        ValidatorUtils.validate(loginBody);
//        String tenantId = loginBody.getTenantId();
//        String username = loginBody.getUsername();
//        String password = loginBody.getPassword();
//        String code = loginBody.getCode();
//        String uuid = loginBody.getUuid();
//
//        boolean captchaEnabled = captchaProperties.getEnable();
//        // 验证码开关
//        if (captchaEnabled) {
//            validateCaptcha(tenantId, username, code, uuid);
//        }
//        LoginUser loginUser = TenantHelper.dynamic(tenantId, () -> {
//            SysUserVo user = loadUserByUsername(username);
//            loginService.checkLogin(LoginType.PASSWORD, tenantId, username, () -> !BCrypt.checkpw(password, user.getPassword()));
//            // 此处可根据登录用户的数据不同 自行创建 loginUser
//            return loginService.buildLoginUser(user);
//        });
//        loginUser.setClientKey(client.getClientKey());
//        loginUser.setDeviceType(client.getDeviceType());
//        SaLoginModel model = new SaLoginModel();
//        model.setDevice(client.getDeviceType());
//        // 自定义分配 不同用户体系 不同 token 授权时间 不设置默认走全局 yml 配置
//        // 例如: 后台用户30分钟过期 app用户1天过期
//        model.setTimeout(client.getTimeout());
//        model.setActiveTimeout(client.getActiveTimeout());
//        model.setExtra(LoginHelper.CLIENT_KEY, client.getClientId());
//        // 生成token
//        LoginHelper.login(loginUser, model);
//
//        LoginVo loginVo = new LoginVo();
//        loginVo.setAccessToken(StpUtil.getTokenValue());
//        loginVo.setExpireIn(StpUtil.getTokenTimeout());
//        loginVo.setClientId(client.getClientId());
//        return loginVo;
//    }

    /**
     * 统一身份认证登录
     * @param body 登录请求体
     * @param client 客户端信息
     * @return 登录结果
     */
    public LoginVo login(String body, SysClientVo client) {
        PasswordLoginBody loginBody = JsonUtils.parseObject(body, PasswordLoginBody.class);
        ValidatorUtils.validate(loginBody);
        String tenantId = loginBody.getTenantId();
        String username = loginBody.getUsername();
        String password = loginBody.getPassword();
        String code = loginBody.getCode();
        String uuid = loginBody.getUuid();

        boolean captchaEnabled = captchaProperties.getEnable();
        // 验证码开关
        if (captchaEnabled) {
            validateCaptcha(tenantId, username, code, uuid);
        }

        // 使用配置文件中的统一身份认证URL
        String url = unifiedAuthUrl;
        log.info("环境: {}, 统一身份认证URL: {}, 允许本地认证回退: {}", activeProfile, url, allowLocalAuth);
        RestTemplate restTemplate = new RestTemplate();
        Map<String, Object> form = new HashMap<>();
        form.put("username",username);
        form.put("password",password);
        byte[] aesKeyBytes = aesKey.getBytes(StandardCharsets.UTF_8);
        try{
            String encrypt = AESUtils.encrypt(JSONObject.toJSONString(form).getBytes(),aesKeyBytes);
            Map<String, Object> map = new HashMap<>();
            map.put("encrypt", encrypt);
            R re = restTemplate.postForObject(url, map, R.class);
            LinkedHashMap<String, String> data = (LinkedHashMap<String, String>) re.getData();
            // 统一身份认证成功
            if(re.getCode() == 200&&data.get("username")!=null){

                if(StringUtils.equals(data.get("username"), username)){
                    try {
                        LoginUser loginUser = TenantHelper.dynamic(tenantId, () -> {
                            SysUserVo user = loadUserByUsername(username);
                            // 统一身份认证成功后，只校验用户名，不校验密码
                            // 此处可根据登录用户的数据不同 自行创建 loginUser
                            return loginService.buildLoginUser(user);
                        });
                        loginUser.setClientKey(client.getClientKey());
                        loginUser.setDeviceType(client.getDeviceType());
                        SaLoginModel model = new SaLoginModel();
                        model.setDevice(client.getDeviceType());
                        // 自定义分配 不同用户体系 不同 token 授权时间 不设置默认走全局 yml 配置
                        // 例如: 后台用户30分钟过期 app用户1天过期
                        model.setTimeout(client.getTimeout());
                        model.setActiveTimeout(client.getActiveTimeout());
                        model.setExtra(LoginHelper.CLIENT_KEY, client.getClientId());
                        // 生成token
                        LoginHelper.login(loginUser, model);

                        LoginVo loginVo = new LoginVo();
                        loginVo.setAccessToken(StpUtil.getTokenValue());
                        loginVo.setExpireIn(StpUtil.getTokenTimeout());
                        loginVo.setClientId(client.getClientId());
                        return loginVo;
                    } catch (UserException ex) {
                        // 统一身份认证成功但本地用户不存在或被停用
                        log.warn("统一身份认证成功，但本地用户校验失败: username={}, error={}", username, ex.getMessage());
                        if ("user.not.exists".equals(ex.getCode())) {
                            // 用户不存在，自动创建停用状态的本地账号
                            return createLocalUserAndLogin(tenantId, username, client);
                        } else if ("user.blocked".equals(ex.getCode())) {
                            throw new UserException("统一身份认证成功，但您的本地账号已被停用。请联系系统管理员处理。");
                        } else {
                            throw ex;
                        }
                    }
                }else{
                    log.warn("统一身份认证返回的用户名与请求用户名不匹配: 请求用户名={}, 返回用户名={}", username, data.get("username"));
                    throw new UserException("统一身份认证失败：返回的用户名与请求不匹配。");
                }
            }else{
                log.warn("统一身份认证失败: code={}, message={}", re.getCode(), re.getMsg());

                // 在开发环境下，允许回退到本地密码认证
                if (allowLocalAuth) {
                    log.info("开发环境下统一身份认证失败，回退到本地密码认证: username={}", username);
                    return performLocalPasswordAuth(tenantId, username, password, client);
                } else {
                    throw new UserException("统一身份认证失败：" + re.getMsg());
                }
            }
        }
        catch (UserException ex) {
            // 直接抛出用户异常，不在这里处理回退逻辑
            throw ex;
        }
        catch (Exception ex)
        {
            log.error("调用统一身份认证接口异常: {}", ex.getMessage(), ex);

            // 在开发环境下，允许回退到本地密码认证
            if (allowLocalAuth) {
                log.info("开发环境下统一身份认证接口异常，回退到本地密码认证: username={}", username);
                return performLocalPasswordAuth(tenantId, username, password, client);
            } else {
                throw new UserException("统一身份认证接口调用异常，请稍后重试。");
            }
        }
    }

    public class AESUtils {

        private static final String AES_ALGORITHM = "AES/ECB/PKCS5Padding";

        // 获取 cipher
        private static Cipher getCipher(byte[] key, int model) throws Exception {
            SecretKeySpec secretKeySpec = new SecretKeySpec(key, "AES");
            Cipher cipher = Cipher.getInstance(AES_ALGORITHM);
            cipher.init(model, secretKeySpec);
            return cipher;
        }

        // AES加密
        public static String encrypt(byte[] data, byte[] key) throws Exception {
            Cipher cipher = getCipher(key, Cipher.ENCRYPT_MODE);
            return Base64.getEncoder().encodeToString(cipher.doFinal(data));
        }

        // AES解密
        public static byte[] decrypt(byte[] data, byte[] key) throws Exception {
            Cipher cipher = getCipher(key, Cipher.DECRYPT_MODE);
            return cipher.doFinal(Base64.getDecoder().decode(data));
        }
    }

        /**
     * 校验验证码
     *
     * @param username 用户名
     * @param code     验证码
     * @param uuid     唯一标识
     */
    private void validateCaptcha(String tenantId, String username, String code, String uuid) {
        String verifyKey = GlobalConstants.CAPTCHA_CODE_KEY + StringUtils.blankToDefault(uuid, "");
        String captcha = RedisUtils.getCacheObject(verifyKey);
        RedisUtils.deleteObject(verifyKey);
        if (captcha == null) {
            loginService.recordLogininfor(tenantId, username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire"));
            throw new CaptchaExpireException();
        }
        if (!code.equalsIgnoreCase(captcha)) {
            loginService.recordLogininfor(tenantId, username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error"));
            throw new CaptchaException();
        }
    }

    private SysUserVo loadUserByUsername(String username) {
        SysUserVo user = userMapper.selectVoOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUserName, username));
        if (ObjectUtil.isNull(user)) {
            log.info("登录用户：{} 不存在.", username);
            throw new UserException("user.not.exists", username);
        } else if (SystemConstants.DISABLE.equals(user.getStatus())) {
            log.info("登录用户：{} 已被停用.", username);
            throw new UserException("user.blocked", username);
        }
        return user;
    }

    /**
     * 创建本地用户并登录
     *
     * @param tenantId 租户ID
     * @param username 用户名
     * @param client   客户端信息
     * @return 登录结果
     */
    private LoginVo createLocalUserAndLogin(String tenantId, String username, SysClientVo client) {
        return TenantHelper.dynamic(tenantId, () -> {
            log.info("统一身份认证成功，本地用户不存在，自动创建停用状态账号: username={}", username);

            // 创建停用状态的本地用户
            SysUserBo newUser = new SysUserBo();
            newUser.setUserName(username);
            newUser.setNickName(username); // 使用用户名作为昵称
            newUser.setUserType("sys_user"); // 系统用户
            newUser.setStatus(SystemConstants.DISABLE); // 设置为停用状态

            // 生成16位随机密码（包含大小写字母、数字和特殊字符）
            String randomPassword = generateSecurePassword();
            newUser.setPassword(BCrypt.hashpw(randomPassword, BCrypt.gensalt())); // 设置随机密码
            newUser.setRemark("统一身份认证自动创建，需要管理员激活。初始密码：" + randomPassword);

            try {
                userService.insertUser(newUser);
                log.info("成功创建本地用户: username={}", username);
            } catch (Exception e) {
                log.error("创建本地用户失败: username={}, error={}", username, e.getMessage());
                throw new UserException("统一身份认证成功，但创建本地账号失败。请联系系统管理员处理。");
            }

            // 使用新创建的用户信息进行登录（但是停用状态，不能登录）
            throw new UserException("统一身份认证成功，已为您创建本地账号，但账号状态为停用。请联系系统管理员激活您的账号后重新登录。");
        });
    }

    /**
     * 执行本地密码认证
     *
     * @param tenantId 租户ID
     * @param username 用户名
     * @param password 密码
     * @param client   客户端信息
     * @return 登录结果
     */
    private LoginVo performLocalPasswordAuth(String tenantId, String username, String password, SysClientVo client) {
        return TenantHelper.dynamic(tenantId, () -> {
            LoginUser loginUser;
            try {
                SysUserVo user = loadUserByUsername(username);
                loginService.checkLogin(LoginType.PASSWORD, tenantId, username, () -> !BCrypt.checkpw(password, user.getPassword()));
                loginUser = loginService.buildLoginUser(user);
            } catch (UserException ex) {
                log.warn("本地密码认证失败: username={}, error={}", username, ex.getMessage());
                throw ex;
            }

            loginUser.setClientKey(client.getClientKey());
            loginUser.setDeviceType(client.getDeviceType());
            SaLoginModel model = new SaLoginModel();
            model.setDevice(client.getDeviceType());
            model.setTimeout(client.getTimeout());
            model.setActiveTimeout(client.getActiveTimeout());
            model.setExtra(LoginHelper.CLIENT_KEY, client.getClientId());
            // 生成token
            LoginHelper.login(loginUser, model);

            LoginVo loginVo = new LoginVo();
            loginVo.setAccessToken(StpUtil.getTokenValue());
            loginVo.setExpireIn(StpUtil.getTokenTimeout());
            loginVo.setClientId(client.getClientId());
            return loginVo;
        });
    }

    /**
     * 生成16位安全随机密码
     * 包含大写字母、小写字母、数字和特殊字符
     *
     * @return 随机密码
     */
    private String generateSecurePassword() {
        // 定义密码字符集
        String upperCase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        String lowerCase = "abcdefghijklmnopqrstuvwxyz";
        String numbers = "0123456789";
        String specialChars = "!@#$%^&*()_+-=";

        StringBuilder password = new StringBuilder();

        // 确保密码包含每种类型的字符
        password.append(RandomUtil.randomChar(upperCase));     // 至少一个大写字母
        password.append(RandomUtil.randomChar(lowerCase));     // 至少一个小写字母
        password.append(RandomUtil.randomChar(numbers));       // 至少一个数字
        password.append(RandomUtil.randomChar(specialChars));  // 至少一个特殊字符

        // 填充剩余位数（12位）
        String allChars = upperCase + lowerCase + numbers + specialChars;
        for (int i = 4; i < 16; i++) {
            password.append(RandomUtil.randomChar(allChars));
        }

        // 打乱密码字符顺序
        return RandomUtil.randomString(password.toString(), 16);
    }

}
