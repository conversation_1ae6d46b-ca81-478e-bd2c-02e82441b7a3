/*
 Navicat Premium Data Transfer

 Source Server         : *************
 Source Server Type    : PostgreSQL
 Source Server Version : 110022
 Source Host           : *************:5432
 Source Catalog        : file_admin
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 110022
 File Encoding         : 65001

 Date: 01/04/2025 16:34:36
*/


-- ----------------------------
-- Table structure for files
-- ----------------------------
DROP TABLE IF EXISTS "public"."files";
CREATE TABLE "public"."files" (
  "id" int8,
  "tenant_id" varchar(20) COLLATE "pg_catalog"."default" DEFAULT '000000'::character varying,
  "create_dept" int8,
  "create_time" timestamp(6),
  "create_by" int8,
  "update_time" timestamp(6),
  "update_by" int8,
  "del_flag" int4 DEFAULT 0,
  "upload_id" varchar(255) COLLATE "pg_catalog"."default",
  "md5" varchar(255) COLLATE "pg_catalog"."default",
  "url" varchar(255) COLLATE "pg_catalog"."default" DEFAULT 0,
  "bucket" varchar(64) COLLATE "pg_catalog"."default",
  "object" varchar(255) COLLATE "pg_catalog"."default",
  "origin_file_name" varchar(255) COLLATE "pg_catalog"."default" DEFAULT 0,
  "size" int8,
  "type" varchar(64) COLLATE "pg_catalog"."default",
  "chunk_size" int8,
  "chunk_count" int2
)
;
COMMENT ON COLUMN "public"."files"."id" IS '主键';
COMMENT ON COLUMN "public"."files"."tenant_id" IS '租户编号';
COMMENT ON COLUMN "public"."files"."upload_id" IS '文件上传id';
COMMENT ON COLUMN "public"."files"."md5" IS '文件计算md5';
COMMENT ON COLUMN "public"."files"."url" IS '文件访问地址';
COMMENT ON COLUMN "public"."files"."bucket" IS '存储桶';
COMMENT ON COLUMN "public"."files"."object" IS 'minio中文件名';
COMMENT ON COLUMN "public"."files"."origin_file_name" IS '原始文件名';
COMMENT ON COLUMN "public"."files"."create_dept" IS '创建部门';
COMMENT ON COLUMN "public"."files"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."files"."create_by" IS '创建人';
COMMENT ON COLUMN "public"."files"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."files"."update_by" IS '更新人';
COMMENT ON COLUMN "public"."files"."del_flag" IS '删除标志';
COMMENT ON COLUMN "public"."files"."size" IS '文件大小';
COMMENT ON COLUMN "public"."files"."type" IS '文件类型';
COMMENT ON COLUMN "public"."files"."chunk_size" IS '分片大小';
COMMENT ON COLUMN "public"."files"."chunk_count" IS '分片数量';
COMMENT ON TABLE "public"."files" IS '测试单表';
