package org.dromara.test;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.crypto.digest.DigestUtil;
import lombok.SneakyThrows;
import org.dromara.miniofile.domain.bo.FileUploadInfoBo;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.io.RandomAccessFile;

public class FileTest {

    private static final Integer CHUNK_SIZE = 1024 * 1024 * 20;

    @SneakyThrows
    public Integer splitFile(String filePath, int chunkSize) {
        RandomAccessFile inputFile = new RandomAccessFile(filePath, "r");
        byte[] buffer = new byte[chunkSize];
        int bytesRead = 0;
        int chunkNumber = 1;

        while((bytesRead = inputFile.read(buffer)) != -1){
            String chunkFilePath = filePath + ".part" + chunkNumber;
            RandomAccessFile outputFile = new RandomAccessFile(chunkFilePath, "rw");
            outputFile.write(buffer, 0, bytesRead);
            outputFile.close();
            chunkNumber++;
        }

        inputFile.close();
        return chunkNumber - 1;
    }

    @Test
    public void fileToMd5() {

        String filePath = "D:\\fileTest\\meeting_02.mp4";

        // 创建文件对象
        File file = FileUtil.file(filePath);

        // 计算文件的 MD5 摘要
        String md5 = DigestUtil.md5Hex(file);

        System.out.println(md5);

    }

    @Test
    public void fileToFileUploadInfoBo() {

        String filePath = "D:\\fileTest\\meeting_02.mp4";

        FileUploadInfoBo fileUploadInfoBo = new FileUploadInfoBo();

        // 创建文件对象
        File file = FileUtil.file(filePath);

        // 计算文件的 MD5 摘要
        String md5 = DigestUtil.md5Hex(file);
        fileUploadInfoBo.setMd5(md5);
        fileUploadInfoBo.setOriginFileName(file.getName());
        fileUploadInfoBo.setChunkCount(splitFile(filePath, CHUNK_SIZE));

        System.out.println(fileUploadInfoBo);

    }

}
