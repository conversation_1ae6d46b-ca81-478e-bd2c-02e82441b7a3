package org.dromara.common.core.utils.file;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;

import java.io.*;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * ZIP文件处理工具类
 *
 * <AUTHOR> Agent
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ZipUtils {

    private static final int BUFFER_SIZE = 8192;

    /**
     * 创建ZIP文件并写入到输出流
     *
     * @param fileMap 文件映射，key为文件名，value为文件输入流
     * @param outputStream 输出流
     * @throws IOException 如果创建ZIP文件失败
     */
    public static void createZip(Map<String, InputStream> fileMap, OutputStream outputStream) throws IOException {
        if (fileMap == null || fileMap.isEmpty()) {
            throw new ServiceException("文件列表不能为空");
        }

        try (ZipOutputStream zipOut = new ZipOutputStream(outputStream)) {
            for (Map.Entry<String, InputStream> entry : fileMap.entrySet()) {
                String fileName = entry.getKey();
                InputStream inputStream = entry.getValue();

                if (inputStream == null) {
                    log.warn("文件 {} 的输入流为空，跳过", fileName);
                    continue;
                }

                try {
                    // 创建ZIP条目
                    ZipEntry zipEntry = new ZipEntry(fileName);
                    zipOut.putNextEntry(zipEntry);

                    // 将文件内容写入ZIP
                    byte[] buffer = new byte[BUFFER_SIZE];
                    int length;
                    while ((length = inputStream.read(buffer)) > 0) {
                        zipOut.write(buffer, 0, length);
                    }

                    zipOut.closeEntry();
                } catch (IOException e) {
                    log.error("添加文件 {} 到ZIP时发生错误: {}", fileName, e.getMessage());
                    throw e;
                } finally {
                    // 关闭输入流
                    try {
                        inputStream.close();
                    } catch (IOException e) {
                        log.warn("关闭文件 {} 的输入流时发生错误: {}", fileName, e.getMessage());
                    }
                }
            }
        }
    }

    /**
     * 生成唯一的文件名，避免重复
     *
     * @param originalName 原始文件名
     * @param existingNames 已存在的文件名集合
     * @return 唯一的文件名
     */
    public static String generateUniqueFileName(String originalName, java.util.Set<String> existingNames) {
        if (!existingNames.contains(originalName)) {
            return originalName;
        }

        String baseName;
        String extension = "";
        int dotIndex = originalName.lastIndexOf('.');
        if (dotIndex > 0) {
            baseName = originalName.substring(0, dotIndex);
            extension = originalName.substring(dotIndex);
        } else {
            baseName = originalName;
        }

        int counter = 1;
        String newName;
        do {
            newName = baseName + "(" + counter + ")" + extension;
            counter++;
        } while (existingNames.contains(newName));

        return newName;
    }

    /**
     * 验证文件名是否安全（防止路径遍历攻击）
     *
     * @param fileName 文件名
     * @return 安全的文件名
     */
    public static String sanitizeFileName(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return "unnamed_file";
        }

        // 移除路径分隔符和其他危险字符
        String sanitized = fileName.replaceAll("[/\\\\:*?\"<>|]", "_");

        // 移除开头的点号（隐藏文件）
        if (sanitized.startsWith(".")) {
            sanitized = "_" + sanitized.substring(1);
        }

        // 限制文件名长度
        if (sanitized.length() > 255) {
            String extension = "";
            int dotIndex = sanitized.lastIndexOf('.');
            if (dotIndex > 0) {
                extension = sanitized.substring(dotIndex);
                sanitized = sanitized.substring(0, Math.min(255 - extension.length(), dotIndex)) + extension;
            } else {
                sanitized = sanitized.substring(0, 255);
            }
        }

        return sanitized;
    }
}
