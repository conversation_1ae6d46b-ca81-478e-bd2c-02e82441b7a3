package org.dromara.basic.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.basic.domain.vo.EmergencyResponseVo;
import org.dromara.basic.domain.bo.EmergencyResponseBo;
import org.dromara.basic.service.IEmergencyResponseService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 应急处置
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/basic/emergencyResponse")
public class EmergencyResponseController extends BaseController {

    private final IEmergencyResponseService emergencyResponseService;

    /**
     * 查询应急处置列表
     */
    @SaCheckPermission("basic:emergencyResponse:list")
    @GetMapping("/list")
    public TableDataInfo<EmergencyResponseVo> list(EmergencyResponseBo bo, PageQuery pageQuery) {
        return emergencyResponseService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出应急处置列表
     */
    @SaCheckPermission("basic:emergencyResponse:export")
    @Log(title = "应急处置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(EmergencyResponseBo bo, HttpServletResponse response) {
        List<EmergencyResponseVo> list = emergencyResponseService.queryList(bo);
        ExcelUtil.exportExcel(list, "应急处置", EmergencyResponseVo.class, response);
    }

    /**
     * 获取应急处置详细信息
     *
     * @param emergencyResponseId 主键
     */
    @SaCheckPermission("basic:emergencyResponse:query")
    @GetMapping("/{emergencyResponseId}")
    public R<EmergencyResponseVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long emergencyResponseId) {
        return R.ok(emergencyResponseService.queryById(emergencyResponseId));
    }

    /**
     * 新增应急处置
     */
    @SaCheckPermission("basic:emergencyResponse:add")
    @Log(title = "应急处置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody EmergencyResponseBo bo) {
        return toAjax(emergencyResponseService.insertByBo(bo));
    }


    /**
     * 修改应急处置
     */
    @SaCheckPermission("basic:emergencyResponse:edit")
    @Log(title = "应急处置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody EmergencyResponseBo bo) {
        return toAjax(emergencyResponseService.updateByBo(bo));
    }

    /**
     * 删除应急处置
     *
     * @param emergencyResponseIds 主键串
     */
    @SaCheckPermission("basic:emergencyResponse:remove")
    @Log(title = "应急处置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{emergencyResponseIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] emergencyResponseIds) {
        return toAjax(emergencyResponseService.deleteWithValidByIds(List.of(emergencyResponseIds), true));
    }
}
