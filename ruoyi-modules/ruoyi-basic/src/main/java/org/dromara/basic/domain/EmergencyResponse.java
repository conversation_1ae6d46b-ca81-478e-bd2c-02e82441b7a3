package org.dromara.basic.domain;

import java.util.List;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.translation.annotation.Translation;
import org.dromara.common.translation.constant.TransConstant;

import java.io.Serial;

/**
 * 应急处置对象 emergency_response
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("emergency_response")
public class EmergencyResponse extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 对象存储主键
     */
    @TableId(value = "emergency_response_id")
    private Long emergencyResponseId;

    /**
     * urlId
     */
    private String urlId;


}
