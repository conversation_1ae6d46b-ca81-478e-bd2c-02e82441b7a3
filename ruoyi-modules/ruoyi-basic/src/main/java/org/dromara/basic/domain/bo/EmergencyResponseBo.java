package org.dromara.basic.domain.bo;

import org.dromara.basic.domain.EmergencyResponse;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import org.dromara.common.translation.annotation.Translation;
import org.dromara.common.translation.constant.TransConstant;

/**
 * 应急处置业务对象 emergency_response
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = EmergencyResponse.class, reverseConvertGenerate = false)
public class EmergencyResponseBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long emergencyResponseId;

    /**
     * 应急图片
     */
    @NotBlank(message = "应急图片不能为空", groups = { AddGroup.class, EditGroup.class })
    private String urlId;


}
