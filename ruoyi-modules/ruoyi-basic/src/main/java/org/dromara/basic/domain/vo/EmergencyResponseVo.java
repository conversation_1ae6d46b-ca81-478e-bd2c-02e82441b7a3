package org.dromara.basic.domain.vo;

import java.util.List;

import org.dromara.common.translation.annotation.Translation;
import org.dromara.common.translation.constant.TransConstant;
import org.dromara.basic.domain.EmergencyResponse;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 应急处置视图对象 emergency_response
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = EmergencyResponse.class)
public class EmergencyResponseVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long emergencyResponseId;

    /**
     * 应急图片
     */
    @ExcelProperty(value = "应急图片")
    private String urlId;


}
