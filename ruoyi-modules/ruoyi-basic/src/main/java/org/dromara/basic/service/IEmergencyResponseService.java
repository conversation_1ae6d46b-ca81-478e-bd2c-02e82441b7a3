package org.dromara.basic.service;

import org.dromara.basic.domain.vo.EmergencyResponseVo;
import org.dromara.basic.domain.bo.EmergencyResponseBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 应急处置Service接口
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
public interface IEmergencyResponseService {

    /**
     * 查询应急处置
     *
     * @param emergencyResponseId 主键
     * @return 应急处置
     */
    EmergencyResponseVo queryById(Long emergencyResponseId);

    /**
     * 分页查询应急处置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 应急处置分页列表
     */
    TableDataInfo<EmergencyResponseVo> queryPageList(EmergencyResponseBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的应急处置列表
     *
     * @param bo 查询条件
     * @return 应急处置列表
     */
    List<EmergencyResponseVo> queryList(EmergencyResponseBo bo);

    /**
     * 新增应急处置
     *
     * @param bo 应急处置
     * @return 是否新增成功
     */
    Boolean insertByBo(EmergencyResponseBo bo);

    /**
     * 修改应急处置
     *
     * @param bo 应急处置
     * @return 是否修改成功
     */
    Boolean updateByBo(EmergencyResponseBo bo);

    /**
     * 校验并批量删除应急处置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
