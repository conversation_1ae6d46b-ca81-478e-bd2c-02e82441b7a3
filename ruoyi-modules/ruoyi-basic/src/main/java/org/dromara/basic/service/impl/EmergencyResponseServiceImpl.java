package org.dromara.basic.service.impl;

import java.util.List;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.basic.domain.bo.EmergencyResponseBo;
import org.dromara.basic.domain.vo.EmergencyResponseVo;
import org.dromara.basic.domain.EmergencyResponse;
import org.dromara.basic.mapper.EmergencyResponseMapper;
import org.dromara.basic.service.IEmergencyResponseService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 应急处置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@RequiredArgsConstructor
@Service
public class EmergencyResponseServiceImpl implements IEmergencyResponseService {

    private final EmergencyResponseMapper baseMapper;

    /**
     * 查询应急处置
     *
     * @param emergencyResponseId 主键
     * @return 应急处置
     */
    @Override
    public EmergencyResponseVo queryById(Long emergencyResponseId){
        return baseMapper.selectVoById(emergencyResponseId);
    }

    /**
     * 分页查询应急处置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 应急处置分页列表
     */
    @Override
    public TableDataInfo<EmergencyResponseVo> queryPageList(EmergencyResponseBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<EmergencyResponse> lqw = buildQueryWrapper(bo);
        Page<EmergencyResponseVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的应急处置列表
     *
     * @param bo 查询条件
     * @return 应急处置列表
     */
    @Override
    public List<EmergencyResponseVo> queryList(EmergencyResponseBo bo) {
        LambdaQueryWrapper<EmergencyResponse> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<EmergencyResponse> buildQueryWrapper(EmergencyResponseBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<EmergencyResponse> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(EmergencyResponse::getEmergencyResponseId);
        if (bo.getUrlId() != null && !bo.getUrlId().isEmpty()) {
            lqw.in(EmergencyResponse::getUrlId, bo.getUrlId());
        }
        return lqw;
    }

    /**
     * 新增应急处置
     *
     * @param bo 应急处置
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(EmergencyResponseBo bo) {
        EmergencyResponse add = MapstructUtils.convert(bo, EmergencyResponse.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setEmergencyResponseId(add.getEmergencyResponseId());
        }
        return flag;
    }

    /**
     * 修改应急处置
     *
     * @param bo 应急处置
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(EmergencyResponseBo bo) {
        EmergencyResponse update = MapstructUtils.convert(bo, EmergencyResponse.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(EmergencyResponse entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除应急处置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
