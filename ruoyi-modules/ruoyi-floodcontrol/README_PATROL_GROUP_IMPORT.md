# 出巡分组导入功能实现说明

## 功能概述

出巡分组导入功能允许用户通过Excel文件批量导入出巡分组数据，支持新增和更新操作。

## 实现的文件

### 1. 核心实现文件

#### 导入监听器
- **文件**: `src/main/java/org/dromara/floodcontrol/listener/PatrolGroupImportListener.java`
- **功能**: 处理Excel数据解析和业务逻辑
- **特性**:
  - 数据验证（必填字段、数据格式、业务规则）
  - 重复数据检查
  - 支持新增和更新模式
  - 详细的错误信息反馈

#### 导入VO类
- **文件**: `src/main/java/org/dromara/floodcontrol/domain/vo/FloodcontrolPatrolGroupImportVo.java`
- **功能**: 定义Excel导入的数据结构
- **特性**:
  - Excel字段映射注解
  - 数据验证注解
  - AutoMapper支持

#### 控制器更新
- **文件**: `src/main/java/org/dromara/floodcontrol/controller/FloodcontrolPatrolGroupController.java`
- **更新内容**:
  - 导入数据接口 `/importData`
  - 导入模板下载接口 `/importTemplate`
  - 权限控制

### 2. 配置文件

#### 菜单权限SQL
- **文件**: `script/sql/patrolGroupMenu.sql`
- **新增**: 出巡分组导入权限 `floodcontrol:patrolGroup:import`

#### 导入模板说明
- **文件**: `script/excel/patrol_group_import_template.md`
- **内容**: 详细的导入字段说明和示例数据

## API接口

### 1. 导入数据
```http
POST /floodcontrol/patrolGroup/importData
Content-Type: multipart/form-data

Parameters:
- file: MultipartFile (Excel文件)
- updateSupport: boolean (是否支持更新已存在数据)
```

### 2. 下载导入模板
```http
POST /floodcontrol/patrolGroup/importTemplate
```

## 数据验证规则

### 必填字段验证
- 线路名称：不能为空
- 巡查分组名称：不能为空

### 业务规则验证
- 开始里程不能大于或等于结束里程
- 巡查分组名称唯一性检查

### 数据格式验证
- 里程数据必须为数字类型
- 时间数据必须为正整数

## 导入流程

1. **文件上传**: 用户上传Excel文件
2. **数据解析**: 使用EasyExcel解析文件内容
3. **数据验证**: 逐行验证数据格式和业务规则
4. **重复检查**: 根据巡查分组名称检查是否存在重复数据
5. **数据处理**: 
   - 新数据：直接插入
   - 重复数据：根据updateSupport参数决定更新或跳过
6. **结果反馈**: 返回导入成功和失败的详细信息

## 错误处理

### 数据验证错误
- 必填字段为空
- 数据格式不正确
- 业务规则不符合

### 业务逻辑错误
- 数据库操作失败
- 重复数据处理

### 系统错误
- 文件解析失败
- 服务异常

## 使用示例

### 1. 准备Excel文件
按照模板格式准备数据，确保必填字段不为空。

### 2. 调用导入接口
```javascript
// 前端调用示例
const formData = new FormData();
formData.append('file', file);
formData.append('updateSupport', true);

fetch('/floodcontrol/patrolGroup/importData', {
    method: 'POST',
    body: formData
}).then(response => response.json())
  .then(data => console.log(data));
```

### 3. 处理导入结果
根据返回的结果信息，显示导入成功和失败的详细信息。

## 性能考虑

- 支持大批量数据导入
- 内存优化的流式处理
- 事务控制确保数据一致性

## 扩展性

- 易于添加新的验证规则
- 支持自定义数据转换逻辑
- 可配置的导入字段映射

## 注意事项

1. Excel文件建议使用.xlsx格式
2. 大批量数据建议分批导入
3. 导入前建议备份现有数据
4. 确保用户具有相应的导入权限
