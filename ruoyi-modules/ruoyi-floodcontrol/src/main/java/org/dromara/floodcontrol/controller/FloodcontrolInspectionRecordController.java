package org.dromara.floodcontrol.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.floodcontrol.domain.vo.FloodcontrolInspectionRecordVo;
import org.dromara.floodcontrol.domain.bo.FloodcontrolInspectionRecordBo;
import org.dromara.floodcontrol.service.IFloodcontrolInspectionRecordService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 巡检记录
 *
 * <AUTHOR>
 * @date 2025-09-22
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/floodcontrol/inspectionRecord")
public class FloodcontrolInspectionRecordController extends BaseController {

    private final IFloodcontrolInspectionRecordService floodcontrolInspectionRecordService;

    /**
     * 查询巡检记录列表
     */
    @SaCheckPermission("floodcontrol:inspectionRecord:list")
    @GetMapping("/list")
    public TableDataInfo<FloodcontrolInspectionRecordVo> list(FloodcontrolInspectionRecordBo bo, PageQuery pageQuery) {
        return floodcontrolInspectionRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出巡检记录列表
     */
    @SaCheckPermission("floodcontrol:inspectionRecord:export")
    @Log(title = "巡检记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(FloodcontrolInspectionRecordBo bo, HttpServletResponse response) {
        List<FloodcontrolInspectionRecordVo> list = floodcontrolInspectionRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "巡检记录", FloodcontrolInspectionRecordVo.class, response);
    }

    /**
     * 获取巡检记录详细信息
     *
     * @param irId 主键
     */
    @SaCheckPermission("floodcontrol:inspectionRecord:query")
    @GetMapping("/{irId}")
    public R<FloodcontrolInspectionRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long irId) {
        return R.ok(floodcontrolInspectionRecordService.queryById(irId));
    }

    /**
     * 新增巡检记录
     */
    @SaCheckPermission("floodcontrol:inspectionRecord:add")
    @Log(title = "巡检记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FloodcontrolInspectionRecordBo bo) {
        return toAjax(floodcontrolInspectionRecordService.insertByBo(bo));
    }

    /**
     * 修改巡检记录
     */
    @SaCheckPermission("floodcontrol:inspectionRecord:edit")
    @Log(title = "巡检记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FloodcontrolInspectionRecordBo bo) {
        return toAjax(floodcontrolInspectionRecordService.updateByBo(bo));
    }

    /**
     * 删除巡检记录
     *
     * @param irIds 主键串
     */
    @SaCheckPermission("floodcontrol:inspectionRecord:remove")
    @Log(title = "巡检记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{irIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] irIds) {
        return toAjax(floodcontrolInspectionRecordService.deleteWithValidByIds(List.of(irIds), true));
    }
}
