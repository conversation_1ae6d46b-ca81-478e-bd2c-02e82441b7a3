package org.dromara.floodcontrol.controller;

import java.util.ArrayList;
import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.common.excel.core.ExcelResult;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.floodcontrol.domain.vo.FloodcontrolPatrolGroupVo;
import org.dromara.floodcontrol.domain.vo.FloodcontrolPatrolGroupImportVo;
import org.dromara.floodcontrol.domain.bo.FloodcontrolPatrolGroupBo;
import org.dromara.floodcontrol.listener.PatrolGroupImportListener;
import org.dromara.floodcontrol.service.IFloodcontrolPatrolGroupService;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 出巡分组
 *
 * <AUTHOR>
 * @date 2025-09-22
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/floodcontrol/patrolGroup")
public class FloodcontrolPatrolGroupController extends BaseController {

    private final IFloodcontrolPatrolGroupService floodcontrolPatrolGroupService;

    /**
     * 查询出巡分组列表
     */
    @SaCheckPermission("floodcontrol:patrolGroup:list")
    @GetMapping("/list")
    public TableDataInfo<FloodcontrolPatrolGroupVo> list(FloodcontrolPatrolGroupBo bo, PageQuery pageQuery) {
        return floodcontrolPatrolGroupService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出出巡分组列表
     */
    @SaCheckPermission("floodcontrol:patrolGroup:export")
    @Log(title = "出巡分组", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(FloodcontrolPatrolGroupBo bo, HttpServletResponse response) {
        List<FloodcontrolPatrolGroupVo> list = floodcontrolPatrolGroupService.queryList(bo);
        ExcelUtil.exportExcel(list, "出巡分组", FloodcontrolPatrolGroupVo.class, response);
    }

    /**
     * 导入数据
     *
     * @param file          导入文件
     * @param updateSupport 是否更新已存在数据
     */
    @Log(title = "出巡分组管理", businessType = BusinessType.IMPORT)
    @SaCheckPermission("system:user:import")
    @PostMapping(value = "/importData", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<Void> importData(@RequestPart("file") MultipartFile file, boolean updateSupport) throws Exception {
        ExcelResult<FloodcontrolPatrolGroupVo> result = ExcelUtil.importExcel(file.getInputStream(), FloodcontrolPatrolGroupVo.class, new PatrolGroupImportListener(updateSupport));
        return R.ok(result.getAnalysis());
    }

    /**
     * 获取导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil.exportExcel(new ArrayList<>(), "出巡分组数据", FloodcontrolPatrolGroupVo.class, response);
    }

    /**
     * 获取出巡分组详细信息
     *
     * @param patrolGroupId 主键
     */
    @SaCheckPermission("floodcontrol:patrolGroup:query")
    @GetMapping("/{patrolGroupId}")
    public R<FloodcontrolPatrolGroupVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long patrolGroupId) {
        return R.ok(floodcontrolPatrolGroupService.queryById(patrolGroupId));
    }

    /**
     * 新增出巡分组
     */
    @SaCheckPermission("floodcontrol:patrolGroup:add")
    @Log(title = "出巡分组", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FloodcontrolPatrolGroupBo bo) {
        return toAjax(floodcontrolPatrolGroupService.insertByBo(bo));
    }

    /**
     * 修改出巡分组
     */
    @SaCheckPermission("floodcontrol:patrolGroup:edit")
    @Log(title = "出巡分组", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FloodcontrolPatrolGroupBo bo) {
        return toAjax(floodcontrolPatrolGroupService.updateByBo(bo));
    }

    /**
     * 删除出巡分组
     *
     * @param patrolGroupIds 主键串
     */
    @SaCheckPermission("floodcontrol:patrolGroup:remove")
    @Log(title = "出巡分组", businessType = BusinessType.DELETE)
    @DeleteMapping("/{patrolGroupIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] patrolGroupIds) {
        return toAjax(floodcontrolPatrolGroupService.deleteWithValidByIds(List.of(patrolGroupIds), true));
    }
}
