package org.dromara.floodcontrol.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 巡检记录对象 floodcontrol_inspection_record
 *
 * <AUTHOR>
 * @date 2025-09-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("floodcontrol_inspection_record")
public class FloodcontrolInspectionRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 巡检记录ID
     */
    @TableId(value = "ir_id")
    private Long irId;

    /**
     * 关联出巡分组
     */
    private Long patrolGroupId;

    /**
     * 关联巡回图
     */
    private Long routeMapId;

    /**
     * 巡检人
     */
    private Long inspectionUserId;

    /**
     * 0=待巡查 1=巡查中 2=已结束 3=未巡查
     */
    private Long inspectionState;

    /**
     * 开始时间
     */
    private Date beginTime;

    /**
     * 实际上道时间
     */
    private Date onlineTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 起始经度
     */
    private String startLng;

    /**
     * 起始纬度
     */
    private String startLat;

    /**
     * 结束经度
     */
    private String endLng;

    /**
     * 结束纬度
     */
    private String endLat;

    /**
     * 总里程(km)
     */
    private Long totalMileage;

    /**
     * 1冒雨 2雨后
     */
    private Long inspectionType;

    /**
     * 是否自定义巡回图 0否 1是
     */
    private Long isCustomize;

    /**
     * 自定义里程
     */
    private String customizeMileage;

    /**
     * 巡查截止时间
     */
    private Date stopTime;

    /**
     * 关联雨量报警Id
     */
    private Long noticeMsgId;


}
