package org.dromara.system.domain.bo;

import org.dromara.system.domain.FloodcontrolPatrolGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 出巡分组业务对象 floodcontrol_patrol_group
 *
 * <AUTHOR> Li
 * @date 2025-09-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FloodcontrolPatrolGroup.class, reverseConvertGenerate = false)
public class FloodcontrolPatrolGroupBo extends BaseEntity {

    /**
     * 出巡分组ID
     */
    @NotNull(message = "出巡分组ID不能为空", groups = { EditGroup.class })
    private Long patrolGroupId;

    /**
     * 工务段id
     */
    private String segmentId;

    /**
     * 工务段名称
     */
    private String segmentName;

    /**
     * 线路id
     */
    private String lineId;

    /**
     * 线路名称
     */
    private String lineName;

    /**
     * 站名
     */
    private String station;

    /**
     * 雨量计点名称
     */
    private String rainGaugePoint;

    /**
     * 雨量计安装里程
     */
    private Long rainGaugeMileage;

    /**
     * 报警影响范围
     */
    private String alarmRange;

    /**
     * 责任车间ID
     */
    private String responsibleWorkshopId;

    /**
     * 责任车间名称
     */
    private String responsibleWorkshopName;

    /**
     * 责任工区ID
     */
    private String responsibleGangId;

    /**
     * 责任工区名称
     */
    private String responsibleGangName;

    /**
     * 防洪重点地点
     */
    private String keyLocations;

    /**
     * 防洪薄弱地段
     */
    private String weakSections;

    /**
     * 巡查分组名称
     */
    private String patrolGroupName;

    /**
     * 开始里程
     */
    private Long startMileage;

    /**
     * 结束里程
     */
    private Long endMileage;

    /**
     * 进入栅栏门位置
     */
    private String fenceGateIn;

    /**
     * 离开栅栏门位置
     */
    private String fenceGateOut;

    /**
     * 巡查长度(公里)
     */
    private Long patrolLength;

    /**
     * 单程用时(分钟)
     */
    private Long oneWayTime;

    /**
     * 工区名称
     */
    private String gangName;


}
