package org.dromara.floodcontrol.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.floodcontrol.domain.FloodcontrolInspectionRecord;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 巡检记录视图对象 floodcontrol_inspection_record
 *
 * <AUTHOR>
 * @date 2025-09-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FloodcontrolInspectionRecord.class)
public class FloodcontrolInspectionRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 巡检记录ID
     */
    @ExcelProperty(value = "巡检记录ID")
    private Long irId;

    /**
     * 关联出巡分组
     */
    @ExcelProperty(value = "关联出巡分组")
    private Long patrolGroupId;

    /**
     * 关联巡回图
     */
    @ExcelProperty(value = "关联巡回图")
    private Long routeMapId;

    /**
     * 巡检人
     */
    @ExcelProperty(value = "巡检人")
    private Long inspectionUserId;

    /**
     * 0=待巡查 1=巡查中 2=已结束 3=未巡查
     */
    @ExcelProperty(value = "0=待巡查 1=巡查中 2=已结束 3=未巡查")
    private Long inspectionState;

    /**
     * 开始时间
     */
    @ExcelProperty(value = "开始时间")
    private Date beginTime;

    /**
     * 实际上道时间
     */
    @ExcelProperty(value = "实际上道时间")
    private Date onlineTime;

    /**
     * 结束时间
     */
    @ExcelProperty(value = "结束时间")
    private Date endTime;

    /**
     * 起始经度
     */
    @ExcelProperty(value = "起始经度")
    private String startLng;

    /**
     * 起始纬度
     */
    @ExcelProperty(value = "起始纬度")
    private String startLat;

    /**
     * 结束经度
     */
    @ExcelProperty(value = "结束经度")
    private String endLng;

    /**
     * 结束纬度
     */
    @ExcelProperty(value = "结束纬度")
    private String endLat;

    /**
     * 总里程(km)
     */
    @ExcelProperty(value = "总里程(km)")
    private Long totalMileage;

    /**
     * 1冒雨 2雨后
     */
    @ExcelProperty(value = "1冒雨 2雨后")
    private Long inspectionType;

    /**
     * 是否自定义巡回图 0否 1是
     */
    @ExcelProperty(value = "是否自定义巡回图 0否 1是")
    private Long isCustomize;

    /**
     * 自定义里程
     */
    @ExcelProperty(value = "自定义里程")
    private String customizeMileage;

    /**
     * 巡查截止时间
     */
    @ExcelProperty(value = "巡查截止时间")
    private Date stopTime;

    /**
     * 关联雨量报警Id
     */
    @ExcelProperty(value = "关联雨量报警Id")
    private Long noticeMsgId;


}
