package org.dromara.floodcontrol.domain.vo;

import org.dromara.floodcontrol.domain.FloodcontrolPatrolGroup;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 出巡分组视图对象 floodcontrol_patrol_group
 *
 * <AUTHOR>
 * @date 2025-09-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FloodcontrolPatrolGroup.class)
public class FloodcontrolPatrolGroupVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 出巡分组ID
     */
    private Long patrolGroupId;

    /**
     * 序号
     */
    @ExcelProperty(value = "序号")
    private Integer seq;

    /**
     * 线路名称
     */
    @ExcelProperty(value = "线路名称")
    private String lineName;

    /**
     * 站名
     */
    @ExcelProperty(value = "车站名称")
    private String station;

    /**
     * 工务段名称
     */
    @ExcelProperty(value = "工务段名称")
    private String segmentName;

    /**
     * 雨量计点名称
     */
    @ExcelProperty(value = "雨量计点")
    private String rainGaugePoint;

    /**
     * 雨量计安装里程
     */
    @ExcelProperty(value = "雨量计安装里程")
    private Long rainGaugeMileage;

    /**
     * 报警影响范围
     */
    @ExcelProperty(value = "报警影响范围")
    private String alarmRange;

    /**
     * 责任车间名称
     */
    @ExcelProperty(value = "责任车间")
    private String responsibleWorkshopName;

    /**
     * 责任工区名称
     */
    @ExcelProperty(value = "责任工区")
    private String responsibleGangName;

    /**
     * 巡查分组名称
     */
    @ExcelProperty(value = "巡查分组名称")
    private String patrolGroupName;

    /**
     * 开始里程
     */
    @ExcelProperty(value = "开始里程")
    private Long startMileage;

    /**
     * 结束里程
     */
    @ExcelProperty(value = "结束里程")
    private Long endMileage;

    /**
     * 进入栅栏门位置
     */
    @ExcelProperty(value = "进入栅栏门位置")
    private String fenceGateIn;

    /**
     * 离开栅栏门位置
     */
    @ExcelProperty(value = "离开栅栏门位置")
    private String fenceGateOut;

    /**
     * 巡查长度(公里)
     */
    @ExcelProperty(value = "巡查长度(公里)")
    private Long patrolLength;

    /**
     * 单程用时(分钟)
     */
    @ExcelProperty(value = "单程用时(分钟)")
    private Long oneWayTime;

    /**
     * 工区名称
     */
    @ExcelProperty(value = "工区名称")
    private String gangName;

    /**
     * 防洪重点地点
     */
    @ExcelProperty(value = "防洪重点地点")
    private String keyLocations;

    /**
     * 防洪薄弱地段
     */
    @ExcelProperty(value = "防洪薄弱地段")
    private String weakSections;


}
