package org.dromara.floodcontrol.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.core.utils.StreamUtils;
import org.dromara.common.core.utils.ValidatorUtils;
import org.dromara.common.excel.core.ExcelListener;
import org.dromara.common.excel.core.ExcelResult;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.floodcontrol.domain.vo.FloodcontrolPatrolGroupImportVo;
import org.dromara.floodcontrol.domain.vo.FloodcontrolPatrolGroupVo;
import org.dromara.floodcontrol.domain.bo.FloodcontrolPatrolGroupBo;
import org.dromara.floodcontrol.service.IFloodcontrolPatrolGroupService;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 出巡分组导入
 *
 * <AUTHOR>
 */
@Slf4j
public class PatrolGroupImportListener extends AnalysisEventListener<FloodcontrolPatrolGroupImportVo> implements ExcelListener<FloodcontrolPatrolGroupImportVo> {

    private final IFloodcontrolPatrolGroupService floodcontrolPatrolGroupService;

    private final Boolean isUpdateSupport;

    private final Long operUserId;

    private int successNum = 0;
    private int failureNum = 0;
    private final StringBuilder successMsg = new StringBuilder();
    private final StringBuilder failureMsg = new StringBuilder();

    public PatrolGroupImportListener(Boolean isUpdateSupport) {
        this.floodcontrolPatrolGroupService = SpringUtils.getBean(IFloodcontrolPatrolGroupService.class);
        this.isUpdateSupport = isUpdateSupport;
        this.operUserId = LoginHelper.getUserId();
    }

    @Override
    public void invoke(FloodcontrolPatrolGroupImportVo importVo, AnalysisContext context) {
        try {
            // 基础数据验证
            String rowNum = String.valueOf(context.readRowHolder().getRowIndex() + 1);

            if (StrUtil.isBlank(importVo.getPatrolGroupName())) {
                failureNum++;
                failureMsg.append("<br/>").append(failureNum).append("、第").append(rowNum)
                    .append("行：巡查分组名称不能为空");
                return;
            }

            // 验证必填字段
            if (StrUtil.isBlank(importVo.getLineName())) {
                failureNum++;
                failureMsg.append("<br/>").append(failureNum).append("、第").append(rowNum)
                    .append("行：线路名称不能为空");
                return;
            }

            // 验证里程数据的合理性
            if (importVo.getStartMileage() != null && importVo.getEndMileage() != null) {
                if (importVo.getStartMileage() >= importVo.getEndMileage()) {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、第").append(rowNum)
                        .append("行：开始里程不能大于或等于结束里程");
                    return;
                }
            }

            // 检查是否存在相同的巡查分组名称
            FloodcontrolPatrolGroupBo queryBo = new FloodcontrolPatrolGroupBo();
            queryBo.setPatrolGroupName(importVo.getPatrolGroupName());
            List<FloodcontrolPatrolGroupVo> existingGroups =
                floodcontrolPatrolGroupService.queryList(queryBo);

            if (ObjectUtil.isNotEmpty(existingGroups)) {
                if (isUpdateSupport) {
                    // 更新现有记录
                    FloodcontrolPatrolGroupVo existingGroup = existingGroups.get(0);
                    FloodcontrolPatrolGroupBo updateBo = convertImportVoToBo(importVo);
                    updateBo.setPatrolGroupId(existingGroup.getPatrolGroupId());
                    updateBo.setUpdateBy(operUserId);

                    ValidatorUtils.validate(updateBo);
                    floodcontrolPatrolGroupService.updateByBo(updateBo);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、巡查分组 ")
                        .append(importVo.getPatrolGroupName()).append(" 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、巡查分组 ")
                        .append(importVo.getPatrolGroupName()).append(" 已存在");
                }
            } else {
                // 新增记录
                FloodcontrolPatrolGroupBo newBo = convertImportVoToBo(importVo);
                newBo.setCreateBy(operUserId);

                ValidatorUtils.validate(newBo);
                floodcontrolPatrolGroupService.insertByBo(newBo);
                successNum++;
                successMsg.append("<br/>").append(successNum).append("、巡查分组 ")
                    .append(importVo.getPatrolGroupName()).append(" 导入成功");
            }
        } catch (Exception e) {
            failureNum++;
            String msg = "<br/>" + failureNum + "、第" + (context.readRowHolder().getRowIndex() + 1) + "行：巡查分组 "
                + HtmlUtil.cleanHtmlTag(importVo.getPatrolGroupName()) + " 导入失败：";
            String message = e.getMessage();
            if (e instanceof ConstraintViolationException cvException) {
                message = StreamUtils.join(cvException.getConstraintViolations(), ConstraintViolation::getMessage, ", ");
            }
            failureMsg.append(msg).append(message);
            log.error(msg, e);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {

    }

    /**
     * 将导入VO转换为业务BO
     */
    private FloodcontrolPatrolGroupBo convertImportVoToBo(FloodcontrolPatrolGroupImportVo importVo) {
        FloodcontrolPatrolGroupBo bo = new FloodcontrolPatrolGroupBo();

        // 基本信息映射
        bo.setLineName(importVo.getLineName());
        bo.setStation(importVo.getStation());
        bo.setSegmentName(importVo.getSegmentName());
        bo.setRainGaugePoint(importVo.getRainGaugePoint());
        bo.setRainGaugeMileage(importVo.getRainGaugeMileage());
        bo.setAlarmRange(importVo.getAlarmRange());
        bo.setResponsibleWorkshopName(importVo.getResponsibleWorkshopName());
        bo.setResponsibleGangName(importVo.getResponsibleGangName());
        bo.setPatrolGroupName(importVo.getPatrolGroupName());
        bo.setStartMileage(importVo.getStartMileage());
        bo.setEndMileage(importVo.getEndMileage());
        bo.setFenceGateIn(importVo.getFenceGateIn());
        bo.setFenceGateOut(importVo.getFenceGateOut());
        bo.setPatrolLength(importVo.getPatrolLength());
        bo.setOneWayTime(importVo.getOneWayTime());
        bo.setGangName(importVo.getGangName());
        bo.setKeyLocations(importVo.getKeyLocations());
        bo.setWeakSections(importVo.getWeakSections());

        return bo;
    }

    @Override
    public ExcelResult<FloodcontrolPatrolGroupImportVo> getExcelResult() {
        return new ExcelResult<>() {

            @Override
            public String getAnalysis() {
                if (failureNum > 0) {
                    failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
                    throw new ServiceException(failureMsg.toString());
                } else {
                    successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
                }
                return successMsg.toString();
            }

            @Override
            public List<FloodcontrolPatrolGroupImportVo> getList() {
                return null;
            }

            @Override
            public List<String> getErrorList() {
                return null;
            }
        };
    }
}
