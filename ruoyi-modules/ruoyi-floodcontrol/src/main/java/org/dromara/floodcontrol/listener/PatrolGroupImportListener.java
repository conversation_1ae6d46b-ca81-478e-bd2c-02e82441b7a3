package org.dromara.floodcontrol.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.BCrypt;
import cn.hutool.http.HtmlUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.core.utils.StreamUtils;
import org.dromara.common.core.utils.ValidatorUtils;
import org.dromara.common.excel.core.ExcelListener;
import org.dromara.common.excel.core.ExcelResult;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.floodcontrol.domain.vo.FloodcontrolPatrolGroupVo;
import org.dromara.floodcontrol.service.IFloodcontrolPatrolGroupService;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 出巡分组导入
 *
 * <AUTHOR>
 */
@Slf4j
public class PatrolGroupImportListener extends AnalysisEventListener<FloodcontrolPatrolGroupVo> implements ExcelListener<FloodcontrolPatrolGroupVo> {

    private final IFloodcontrolPatrolGroupService floodcontrolPatrolGroupService;

//    private final String password;
//
    private final Boolean isUpdateSupport;

    private final Long operUserId;

    private int successNum = 0;
    private int failureNum = 0;
    private final StringBuilder successMsg = new StringBuilder();
    private final StringBuilder failureMsg = new StringBuilder();

    public PatrolGroupImportListener(Boolean isUpdateSupport) {
        this.floodcontrolPatrolGroupService = SpringUtils.getBean(IFloodcontrolPatrolGroupService.class);
        this.isUpdateSupport = isUpdateSupport;
        this.operUserId = LoginHelper.getUserId();
    }

    @Override
    public void invoke(FloodcontrolPatrolGroupVo patrolGroupVo, AnalysisContext context) {
//        FloodcontrolPatrolGroupVo patrolGroupVo = this.floodcontrolPatrolGroupService.selectUserByUserName(userVo.getUserName());
//        PatrolGroupVo PatrolGroup = this.floodcontrolPatrolGroupService.queryByDeviceId(patrolGroupVo.getDeviceId());
//        try {
//            // 验证是否存在这个用户
//            if (ObjectUtil.isNull(sysUser)) {
//                SysUserBo user = BeanUtil.toBean(userVo, SysUserBo.class);
//                ValidatorUtils.validate(user);
//                user.setPassword(password);
//                user.setCreateBy(operUserId);
//                floodcontrolPatrolGroupService.insertUser(user);
//                successNum++;
//                successMsg.append("<br/>").append(successNum).append("、账号 ").append(user.getUserName()).append(" 导入成功");
//            } else if (isUpdateSupport) {
//                Long userId = sysUser.getUserId();
//                SysUserBo user = BeanUtil.toBean(userVo, SysUserBo.class);
//                user.setUserId(userId);
//                ValidatorUtils.validate(user);
//                floodcontrolPatrolGroupService.checkUserAllowed(user.getUserId());
//                floodcontrolPatrolGroupService.checkUserDataScope(user.getUserId());
//                user.setUpdateBy(operUserId);
//                floodcontrolPatrolGroupService.updateUser(user);
//                successNum++;
//                successMsg.append("<br/>").append(successNum).append("、账号 ").append(user.getUserName()).append(" 更新成功");
//            } else {
//                failureNum++;
//                failureMsg.append("<br/>").append(failureNum).append("、账号 ").append(sysUser.getUserName()).append(" 已存在");
//            }
//        } catch (Exception e) {
//            failureNum++;
//            String msg = "<br/>" + failureNum + "、账号 " + HtmlUtil.cleanHtmlTag(userVo.getUserName()) + " 导入失败：";
//            String message = e.getMessage();
//            if (e instanceof ConstraintViolationException cvException) {
//                message = StreamUtils.join(cvException.getConstraintViolations(), ConstraintViolation::getMessage, ", ");
//            }
//            failureMsg.append(msg).append(message);
//            log.error(msg, e);
//        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {

    }

    @Override
    public ExcelResult<FloodcontrolPatrolGroupVo> getExcelResult() {
        return new ExcelResult<>() {

            @Override
            public String getAnalysis() {
                if (failureNum > 0) {
                    failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
                    throw new ServiceException(failureMsg.toString());
                } else {
                    successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
                }
                return successMsg.toString();
            }

            @Override
            public List<FloodcontrolPatrolGroupVo> getList() {
                return null;
            }

            @Override
            public List<String> getErrorList() {
                return null;
            }
        };
    }
}
