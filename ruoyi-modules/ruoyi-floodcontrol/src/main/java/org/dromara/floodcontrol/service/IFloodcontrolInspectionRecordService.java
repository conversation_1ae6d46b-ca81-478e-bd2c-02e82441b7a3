package org.dromara.floodcontrol.service;

import org.dromara.floodcontrol.domain.vo.FloodcontrolInspectionRecordVo;
import org.dromara.floodcontrol.domain.bo.FloodcontrolInspectionRecordBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 巡检记录Service接口
 *
 * <AUTHOR>
 * @date 2025-09-22
 */
public interface IFloodcontrolInspectionRecordService {

    /**
     * 查询巡检记录
     *
     * @param irId 主键
     * @return 巡检记录
     */
    FloodcontrolInspectionRecordVo queryById(Long irId);

    /**
     * 分页查询巡检记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 巡检记录分页列表
     */
    TableDataInfo<FloodcontrolInspectionRecordVo> queryPageList(FloodcontrolInspectionRecordBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的巡检记录列表
     *
     * @param bo 查询条件
     * @return 巡检记录列表
     */
    List<FloodcontrolInspectionRecordVo> queryList(FloodcontrolInspectionRecordBo bo);

    /**
     * 新增巡检记录
     *
     * @param bo 巡检记录
     * @return 是否新增成功
     */
    Boolean insertByBo(FloodcontrolInspectionRecordBo bo);

    /**
     * 修改巡检记录
     *
     * @param bo 巡检记录
     * @return 是否修改成功
     */
    Boolean updateByBo(FloodcontrolInspectionRecordBo bo);

    /**
     * 校验并批量删除巡检记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
