package org.dromara.system.service;

import org.dromara.system.domain.vo.FloodcontrolPatrolGroupVo;
import org.dromara.system.domain.bo.FloodcontrolPatrolGroupBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 出巡分组Service接口
 *
 * <AUTHOR> Li
 * @date 2025-09-22
 */
public interface IFloodcontrolPatrolGroupService {

    /**
     * 查询出巡分组
     *
     * @param patrolGroupId 主键
     * @return 出巡分组
     */
    FloodcontrolPatrolGroupVo queryById(Long patrolGroupId);

    /**
     * 分页查询出巡分组列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 出巡分组分页列表
     */
    TableDataInfo<FloodcontrolPatrolGroupVo> queryPageList(FloodcontrolPatrolGroupBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的出巡分组列表
     *
     * @param bo 查询条件
     * @return 出巡分组列表
     */
    List<FloodcontrolPatrolGroupVo> queryList(FloodcontrolPatrolGroupBo bo);

    /**
     * 新增出巡分组
     *
     * @param bo 出巡分组
     * @return 是否新增成功
     */
    Boolean insertByBo(FloodcontrolPatrolGroupBo bo);

    /**
     * 修改出巡分组
     *
     * @param bo 出巡分组
     * @return 是否修改成功
     */
    Boolean updateByBo(FloodcontrolPatrolGroupBo bo);

    /**
     * 校验并批量删除出巡分组信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
