package org.dromara.floodcontrol.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.floodcontrol.domain.bo.FloodcontrolInspectionRecordBo;
import org.dromara.floodcontrol.domain.vo.FloodcontrolInspectionRecordVo;
import org.dromara.floodcontrol.domain.FloodcontrolInspectionRecord;
import org.dromara.floodcontrol.mapper.FloodcontrolInspectionRecordMapper;
import org.dromara.floodcontrol.service.IFloodcontrolInspectionRecordService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 巡检记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-22
 */
@RequiredArgsConstructor
@Service
public class FloodcontrolInspectionRecordServiceImpl implements IFloodcontrolInspectionRecordService {

    private final FloodcontrolInspectionRecordMapper baseMapper;

    /**
     * 查询巡检记录
     *
     * @param irId 主键
     * @return 巡检记录
     */
    @Override
    public FloodcontrolInspectionRecordVo queryById(Long irId){
        return baseMapper.selectVoById(irId);
    }

    /**
     * 分页查询巡检记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 巡检记录分页列表
     */
    @Override
    public TableDataInfo<FloodcontrolInspectionRecordVo> queryPageList(FloodcontrolInspectionRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FloodcontrolInspectionRecord> lqw = buildQueryWrapper(bo);
        Page<FloodcontrolInspectionRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的巡检记录列表
     *
     * @param bo 查询条件
     * @return 巡检记录列表
     */
    @Override
    public List<FloodcontrolInspectionRecordVo> queryList(FloodcontrolInspectionRecordBo bo) {
        LambdaQueryWrapper<FloodcontrolInspectionRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FloodcontrolInspectionRecord> buildQueryWrapper(FloodcontrolInspectionRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FloodcontrolInspectionRecord> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(FloodcontrolInspectionRecord::getIrId);
        lqw.eq(bo.getPatrolGroupId() != null, FloodcontrolInspectionRecord::getPatrolGroupId, bo.getPatrolGroupId());
        lqw.eq(bo.getRouteMapId() != null, FloodcontrolInspectionRecord::getRouteMapId, bo.getRouteMapId());
        lqw.eq(bo.getInspectionUserId() != null, FloodcontrolInspectionRecord::getInspectionUserId, bo.getInspectionUserId());
        lqw.eq(bo.getInspectionState() != null, FloodcontrolInspectionRecord::getInspectionState, bo.getInspectionState());
        lqw.eq(bo.getBeginTime() != null, FloodcontrolInspectionRecord::getBeginTime, bo.getBeginTime());
        lqw.eq(bo.getOnlineTime() != null, FloodcontrolInspectionRecord::getOnlineTime, bo.getOnlineTime());
        lqw.eq(bo.getEndTime() != null, FloodcontrolInspectionRecord::getEndTime, bo.getEndTime());
        lqw.eq(StringUtils.isNotBlank(bo.getStartLng()), FloodcontrolInspectionRecord::getStartLng, bo.getStartLng());
        lqw.eq(StringUtils.isNotBlank(bo.getStartLat()), FloodcontrolInspectionRecord::getStartLat, bo.getStartLat());
        lqw.eq(StringUtils.isNotBlank(bo.getEndLng()), FloodcontrolInspectionRecord::getEndLng, bo.getEndLng());
        lqw.eq(StringUtils.isNotBlank(bo.getEndLat()), FloodcontrolInspectionRecord::getEndLat, bo.getEndLat());
        lqw.eq(bo.getTotalMileage() != null, FloodcontrolInspectionRecord::getTotalMileage, bo.getTotalMileage());
        lqw.eq(bo.getInspectionType() != null, FloodcontrolInspectionRecord::getInspectionType, bo.getInspectionType());
        lqw.eq(bo.getIsCustomize() != null, FloodcontrolInspectionRecord::getIsCustomize, bo.getIsCustomize());
        lqw.eq(StringUtils.isNotBlank(bo.getCustomizeMileage()), FloodcontrolInspectionRecord::getCustomizeMileage, bo.getCustomizeMileage());
        lqw.eq(bo.getStopTime() != null, FloodcontrolInspectionRecord::getStopTime, bo.getStopTime());
        lqw.eq(bo.getNoticeMsgId() != null, FloodcontrolInspectionRecord::getNoticeMsgId, bo.getNoticeMsgId());
        return lqw;
    }

    /**
     * 新增巡检记录
     *
     * @param bo 巡检记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(FloodcontrolInspectionRecordBo bo) {
        FloodcontrolInspectionRecord add = MapstructUtils.convert(bo, FloodcontrolInspectionRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setIrId(add.getIrId());
        }
        return flag;
    }

    /**
     * 修改巡检记录
     *
     * @param bo 巡检记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(FloodcontrolInspectionRecordBo bo) {
        FloodcontrolInspectionRecord update = MapstructUtils.convert(bo, FloodcontrolInspectionRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FloodcontrolInspectionRecord entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除巡检记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
