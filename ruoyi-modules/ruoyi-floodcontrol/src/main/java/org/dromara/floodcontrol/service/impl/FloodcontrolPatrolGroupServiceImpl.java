package org.dromara.system.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.system.domain.bo.FloodcontrolPatrolGroupBo;
import org.dromara.system.domain.vo.FloodcontrolPatrolGroupVo;
import org.dromara.system.domain.FloodcontrolPatrolGroup;
import org.dromara.system.mapper.FloodcontrolPatrolGroupMapper;
import org.dromara.system.service.IFloodcontrolPatrolGroupService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 出巡分组Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-09-22
 */
@RequiredArgsConstructor
@Service
public class FloodcontrolPatrolGroupServiceImpl implements IFloodcontrolPatrolGroupService {

    private final FloodcontrolPatrolGroupMapper baseMapper;

    /**
     * 查询出巡分组
     *
     * @param patrolGroupId 主键
     * @return 出巡分组
     */
    @Override
    public FloodcontrolPatrolGroupVo queryById(Long patrolGroupId){
        return baseMapper.selectVoById(patrolGroupId);
    }

    /**
     * 分页查询出巡分组列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 出巡分组分页列表
     */
    @Override
    public TableDataInfo<FloodcontrolPatrolGroupVo> queryPageList(FloodcontrolPatrolGroupBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FloodcontrolPatrolGroup> lqw = buildQueryWrapper(bo);
        Page<FloodcontrolPatrolGroupVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的出巡分组列表
     *
     * @param bo 查询条件
     * @return 出巡分组列表
     */
    @Override
    public List<FloodcontrolPatrolGroupVo> queryList(FloodcontrolPatrolGroupBo bo) {
        LambdaQueryWrapper<FloodcontrolPatrolGroup> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FloodcontrolPatrolGroup> buildQueryWrapper(FloodcontrolPatrolGroupBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FloodcontrolPatrolGroup> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(FloodcontrolPatrolGroup::getPatrolGroupId);
        lqw.eq(StringUtils.isNotBlank(bo.getSegmentId()), FloodcontrolPatrolGroup::getSegmentId, bo.getSegmentId());
        lqw.like(StringUtils.isNotBlank(bo.getSegmentName()), FloodcontrolPatrolGroup::getSegmentName, bo.getSegmentName());
        lqw.eq(StringUtils.isNotBlank(bo.getLineId()), FloodcontrolPatrolGroup::getLineId, bo.getLineId());
        lqw.like(StringUtils.isNotBlank(bo.getLineName()), FloodcontrolPatrolGroup::getLineName, bo.getLineName());
        lqw.eq(StringUtils.isNotBlank(bo.getStation()), FloodcontrolPatrolGroup::getStation, bo.getStation());
        lqw.eq(StringUtils.isNotBlank(bo.getRainGaugePoint()), FloodcontrolPatrolGroup::getRainGaugePoint, bo.getRainGaugePoint());
        lqw.eq(bo.getRainGaugeMileage() != null, FloodcontrolPatrolGroup::getRainGaugeMileage, bo.getRainGaugeMileage());
        lqw.eq(StringUtils.isNotBlank(bo.getAlarmRange()), FloodcontrolPatrolGroup::getAlarmRange, bo.getAlarmRange());
        lqw.eq(StringUtils.isNotBlank(bo.getResponsibleWorkshopId()), FloodcontrolPatrolGroup::getResponsibleWorkshopId, bo.getResponsibleWorkshopId());
        lqw.like(StringUtils.isNotBlank(bo.getResponsibleWorkshopName()), FloodcontrolPatrolGroup::getResponsibleWorkshopName, bo.getResponsibleWorkshopName());
        lqw.eq(StringUtils.isNotBlank(bo.getResponsibleGangId()), FloodcontrolPatrolGroup::getResponsibleGangId, bo.getResponsibleGangId());
        lqw.like(StringUtils.isNotBlank(bo.getResponsibleGangName()), FloodcontrolPatrolGroup::getResponsibleGangName, bo.getResponsibleGangName());
        lqw.eq(StringUtils.isNotBlank(bo.getKeyLocations()), FloodcontrolPatrolGroup::getKeyLocations, bo.getKeyLocations());
        lqw.eq(StringUtils.isNotBlank(bo.getWeakSections()), FloodcontrolPatrolGroup::getWeakSections, bo.getWeakSections());
        lqw.like(StringUtils.isNotBlank(bo.getPatrolGroupName()), FloodcontrolPatrolGroup::getPatrolGroupName, bo.getPatrolGroupName());
        lqw.eq(bo.getStartMileage() != null, FloodcontrolPatrolGroup::getStartMileage, bo.getStartMileage());
        lqw.eq(bo.getEndMileage() != null, FloodcontrolPatrolGroup::getEndMileage, bo.getEndMileage());
        lqw.eq(StringUtils.isNotBlank(bo.getFenceGateIn()), FloodcontrolPatrolGroup::getFenceGateIn, bo.getFenceGateIn());
        lqw.eq(StringUtils.isNotBlank(bo.getFenceGateOut()), FloodcontrolPatrolGroup::getFenceGateOut, bo.getFenceGateOut());
        lqw.eq(bo.getPatrolLength() != null, FloodcontrolPatrolGroup::getPatrolLength, bo.getPatrolLength());
        lqw.eq(bo.getOneWayTime() != null, FloodcontrolPatrolGroup::getOneWayTime, bo.getOneWayTime());
        lqw.like(StringUtils.isNotBlank(bo.getGangName()), FloodcontrolPatrolGroup::getGangName, bo.getGangName());
        return lqw;
    }

    /**
     * 新增出巡分组
     *
     * @param bo 出巡分组
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(FloodcontrolPatrolGroupBo bo) {
        FloodcontrolPatrolGroup add = MapstructUtils.convert(bo, FloodcontrolPatrolGroup.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setPatrolGroupId(add.getPatrolGroupId());
        }
        return flag;
    }

    /**
     * 修改出巡分组
     *
     * @param bo 出巡分组
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(FloodcontrolPatrolGroupBo bo) {
        FloodcontrolPatrolGroup update = MapstructUtils.convert(bo, FloodcontrolPatrolGroup.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FloodcontrolPatrolGroup entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除出巡分组信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
