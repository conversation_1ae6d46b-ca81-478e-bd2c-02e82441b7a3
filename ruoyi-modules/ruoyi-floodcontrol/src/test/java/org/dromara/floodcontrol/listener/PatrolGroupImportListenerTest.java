package org.dromara.floodcontrol.listener;

import org.dromara.floodcontrol.domain.vo.FloodcontrolPatrolGroupImportVo;
import org.dromara.floodcontrol.domain.bo.FloodcontrolPatrolGroupBo;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 出巡分组导入监听器测试
 *
 * <AUTHOR>
 */
class PatrolGroupImportListenerTest {

    private PatrolGroupImportListener listener;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        listener = new PatrolGroupImportListener(false);
    }

    @Test
    void testCreateImportVo() {
        // 创建测试数据
        FloodcontrolPatrolGroupImportVo importVo = new FloodcontrolPatrolGroupImportVo();
        importVo.setLineName("京沪线");
        importVo.setStation("北京站");
        importVo.setSegmentName("北京工务段");
        importVo.setPatrolGroupName("北京站巡查组");
        importVo.setStartMileage(100L);
        importVo.setEndMileage(105L);
        importVo.setPatrolLength(5L);
        importVo.setOneWayTime(30L);

        // 验证数据
        assertNotNull(importVo);
        assertEquals("京沪线", importVo.getLineName());
        assertEquals("北京站巡查组", importVo.getPatrolGroupName());
        assertEquals(100L, importVo.getStartMileage());
        assertEquals(105L, importVo.getEndMileage());
    }

    @Test
    void testValidateRequiredFields() {
        FloodcontrolPatrolGroupImportVo importVo = new FloodcontrolPatrolGroupImportVo();
        
        // 测试必填字段验证
        assertNull(importVo.getLineName());
        assertNull(importVo.getPatrolGroupName());
        
        // 设置必填字段
        importVo.setLineName("京沪线");
        importVo.setPatrolGroupName("测试巡查组");
        
        assertEquals("京沪线", importVo.getLineName());
        assertEquals("测试巡查组", importVo.getPatrolGroupName());
    }

    @Test
    void testMileageValidation() {
        FloodcontrolPatrolGroupImportVo importVo = new FloodcontrolPatrolGroupImportVo();
        
        // 测试里程数据
        importVo.setStartMileage(100L);
        importVo.setEndMileage(105L);
        
        assertTrue(importVo.getStartMileage() < importVo.getEndMileage(), 
                  "开始里程应该小于结束里程");
    }

    @Test
    void testInvalidMileageData() {
        FloodcontrolPatrolGroupImportVo importVo = new FloodcontrolPatrolGroupImportVo();
        
        // 测试无效的里程数据
        importVo.setStartMileage(105L);
        importVo.setEndMileage(100L);
        
        assertFalse(importVo.getStartMileage() < importVo.getEndMileage(), 
                   "开始里程不应该大于结束里程");
    }
}
