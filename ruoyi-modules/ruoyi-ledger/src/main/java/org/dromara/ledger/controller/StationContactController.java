package org.dromara.ledger.controller;

import java.util.ArrayList;
import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.validation.annotation.Validated;
import org.springframework.http.MediaType;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.excel.core.ExcelResult;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.EasyExcel;
import org.dromara.ledger.domain.vo.StationContactVo;
import org.dromara.ledger.domain.bo.StationContactBo;
import org.dromara.ledger.service.IStationContactService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 车站通讯录
 *
 * <AUTHOR> Li
 * @date 2025-09-17
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/ledger/station_contact")
public class StationContactController extends BaseController {

    private final IStationContactService stationContactService;

    /**
     * 查询车站通讯录列表
     */
    @SaCheckPermission("ledger:station_contact:list")
    @GetMapping("/list")
    public TableDataInfo<StationContactVo> list(StationContactBo bo, PageQuery pageQuery) {
        return stationContactService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出车站通讯录列表
     */
    @SaCheckPermission("ledger:station_contact:export")
    @Log(title = "车站通讯录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StationContactBo bo, HttpServletResponse response) {
        List<StationContactVo> list = stationContactService.queryList(bo);
        ExcelUtil.exportExcel(list, "车站通讯录", StationContactVo.class, response);
    }

    /**
     * 获取车站通讯录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("ledger:station_contact:query")
    @GetMapping("/{id}")
    public R<StationContactVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(stationContactService.queryById(id));
    }

    /**
     * 新增车站通讯录
     */
    @SaCheckPermission("ledger:station_contact:add")
    @Log(title = "车站通讯录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StationContactBo bo) {
        return toAjax(stationContactService.insertByBo(bo));
    }

    /**
     * 修改车站通讯录
     */
    @SaCheckPermission("ledger:station_contact:edit")
    @Log(title = "车站通讯录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StationContactBo bo) {
        return toAjax(stationContactService.updateByBo(bo));
    }

    /**
     * 删除车站通讯录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("ledger:station_contact:remove")
    @Log(title = "车站通讯录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(stationContactService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 导入车站通讯录
     */
    @SaCheckPermission("ledger:station_contact:import")
    @Log(title = "车站通讯录", businessType = BusinessType.IMPORT)
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<String> importData(@RequestPart("file") MultipartFile file,
                              @RequestParam(value = "updateSupport", defaultValue = "false") Boolean updateSupport) throws Exception {
        try {
            // 校验文件类型
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || (!originalFilename.endsWith(".xlsx") && !originalFilename.endsWith(".xls"))) {
                return R.fail("仅支持.xlsx和.xls格式的Excel文件");
            }

            // 读取Excel数据
            List<StationContactVo> dataList = EasyExcel.read(file.getInputStream())
                    .head(StationContactVo.class)
                    .sheet()
                    .doReadSync();

            if (dataList == null || dataList.isEmpty()) {
                return R.fail("导入数据为空，请检查Excel文件内容。<br/>请确保：<br/>1. 文件不为空<br/>2. 列名称与模板一致（线别、车站名称、办公室电话、手机号码、备注）");
            }

            String result = stationContactService.importData(dataList, updateSupport);
            return R.ok(result);
        } catch (Exception e) {
            // 记录详细错误信息
            String errorMsg = "导入失败：" + e.getMessage();
            if (e.getMessage().contains("ExcelAnalysisException")) {
                errorMsg += "<br/>请检查Excel文件格式是否与模板一致";
            }
            return R.fail(errorMsg);
        }
    }

    /**
     * 下载车站通讯录导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil.exportExcel(new ArrayList<>(), "车站通讯录", StationContactVo.class, response);
    }
}