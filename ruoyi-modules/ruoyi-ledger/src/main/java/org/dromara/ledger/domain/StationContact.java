package org.dromara.ledger.domain;


import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 车站通讯录对象 station_contact
 *
 * <AUTHOR>
 * @date 2025-09-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("station_contact")
public class StationContact extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 线别
     */
    private String lineName;

    /**
     * 车站名称
     */
    private String stationName;

    /**
     * 办公室电话
     */
    private String officePhone;

    /**
     * 手机号码
     */
    private String mobilePhone;

}