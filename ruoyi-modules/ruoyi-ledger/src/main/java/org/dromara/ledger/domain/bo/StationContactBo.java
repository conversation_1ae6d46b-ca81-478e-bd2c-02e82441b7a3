package org.dromara.ledger.domain.bo;

import org.dromara.ledger.domain.StationContact;
import org.dromara.common.tenant.core.TenantEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 车站通讯录业务对象 station_contact
 *
 * <AUTHOR> Li
 * @date 2025-09-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = StationContact.class, reverseConvertGenerate = false)
public class StationContactBo extends TenantEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 线别
     */
    @NotBlank(message = "线别不能为空", groups = { AddGroup.class, EditGroup.class })
    private String lineName;

    /**
     * 车站名称
     */
    @NotBlank(message = "车站名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String stationName;

    /**
     * 办公室电话
     */
    private String officePhone;

    /**
     * 手机号码
     */
    private String mobilePhone;




}