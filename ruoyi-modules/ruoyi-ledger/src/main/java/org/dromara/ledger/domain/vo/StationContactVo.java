package org.dromara.ledger.domain.vo;


import org.dromara.ledger.domain.StationContact;
import org.dromara.ledger.domain.bo.StationContactBo;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 车站通讯录视图对象 station_contact
 *
 * <AUTHOR> Li
 * @date 2025-09-17
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = StationContact.class)
//@AutoMapper(target = StationContactBo.class)
public class StationContactVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 线别
     */
    @ExcelProperty(value = "线别")
    private String lineName;

    /**
     * 车站名称
     */
    @ExcelProperty(value = "车站名称")
    private String stationName;

    /**
     * 办公室电话
     */
    @ExcelProperty(value = "办公室电话")
    private String officePhone;

    /**
     * 手机号码
     */
    @ExcelProperty(value = "手机号码")
    private String mobilePhone;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


}