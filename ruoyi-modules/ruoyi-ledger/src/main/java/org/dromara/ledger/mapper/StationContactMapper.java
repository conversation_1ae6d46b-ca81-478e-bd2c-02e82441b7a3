package org.dromara.ledger.mapper;

import org.dromara.ledger.domain.StationContact;
import org.dromara.ledger.domain.bo.StationContactBo;
import org.dromara.ledger.domain.vo.StationContactVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 车站通讯录Mapper接口
 *
 * <AUTHOR> Li
 * @date 2025-09-17
 */
public interface StationContactMapper extends BaseMapperPlus<StationContact, StationContactVo> {

    @Mapper
    interface StationContactMapStructMapper {
        StationContactMapStructMapper INSTANCE = Mappers.getMapper(StationContactMapStructMapper.class);

        StationContactBo toBo(StationContactVo vo);
        StationContactVo toVo(StationContactBo bo);
    }
}