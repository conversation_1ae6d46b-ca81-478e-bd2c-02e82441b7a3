package org.dromara.ledger.service;

import org.dromara.ledger.domain.vo.StationContactVo;
import org.dromara.ledger.domain.bo.StationContactBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 车站通讯录Service接口
 *
 * <AUTHOR> Li
 * @date 2025-09-17
 */
public interface IStationContactService {

    /**
     * 查询车站通讯录
     *
     * @param id 主键
     * @return 车站通讯录
     */
    StationContactVo queryById(Long id);

    /**
     * 分页查询车站通讯录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 车站通讯录分页列表
     */
    TableDataInfo<StationContactVo> queryPageList(StationContactBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的车站通讯录列表
     *
     * @param bo 查询条件
     * @return 车站通讯录列表
     */
    List<StationContactVo> queryList(StationContactBo bo);

    /**
     * 新增车站通讯录
     *
     * @param bo 车站通讯录
     * @return 是否新增成功
     */
    Boolean insertByBo(StationContactBo bo);

    /**
     * 修改车站通讯录
     *
     * @param bo 车站通讯录
     * @return 是否修改成功
     */
    Boolean updateByBo(StationContactBo bo);

    /**
     * 校验并批量删除车站通讯录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量导入车站通讯录
     *
     * @param list 车站通讯录数据列表
     * @param updateSupport 是否支持更新
     * @return 导入结果消息
     */
    String importData(List<StationContactVo> list, Boolean updateSupport);
}