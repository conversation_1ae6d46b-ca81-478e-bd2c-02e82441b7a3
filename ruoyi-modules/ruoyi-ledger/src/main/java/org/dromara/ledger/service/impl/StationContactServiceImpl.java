package org.dromara.ledger.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.ledger.domain.bo.StationContactBo;
import org.dromara.ledger.domain.vo.StationContactVo;
import org.dromara.ledger.domain.StationContact;
import org.dromara.ledger.mapper.StationContactMapper;
import org.dromara.ledger.service.IStationContactService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 车站通讯录Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-09-17
 */
@RequiredArgsConstructor
@Service
public class StationContactServiceImpl implements IStationContactService {

    private final StationContactMapper baseMapper;

    /**
     * 查询车站通讯录
     *
     * @param id 主键
     * @return 车站通讯录
     */
    @Override
    public StationContactVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询车站通讯录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 车站通讯录分页列表
     */
    @Override
    public TableDataInfo<StationContactVo> queryPageList(StationContactBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<StationContact> lqw = buildQueryWrapper(bo);
        Page<StationContactVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的车站通讯录列表
     *
     * @param bo 查询条件
     * @return 车站通讯录列表
     */
    @Override
    public List<StationContactVo> queryList(StationContactBo bo) {
        LambdaQueryWrapper<StationContact> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<StationContact> buildQueryWrapper(StationContactBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<StationContact> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(StationContact::getId);
        lqw.like(StringUtils.isNotBlank(bo.getLineName()), StationContact::getLineName, bo.getLineName());
        lqw.like(StringUtils.isNotBlank(bo.getStationName()), StationContact::getStationName, bo.getStationName());
        return lqw;
    }

    /**
     * 新增车站通讯录
     *
     * @param bo 车站通讯录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(StationContactBo bo) {
        StationContact add = MapstructUtils.convert(bo, StationContact.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改车站通讯录
     *
     * @param bo 车站通讯录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(StationContactBo bo) {
        StationContact update = MapstructUtils.convert(bo, StationContact.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(StationContact entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除车站通讯录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 批量导入车站通讯录
     *
     * @param list 车站通讯录数据列表
     * @param updateSupport 是否支持更新
     * @return 导入结果消息
     */
    @Override
    public String importData(List<StationContactVo> list, Boolean updateSupport) {
        if (list == null || list.isEmpty()) {
            return "导入数据为空";
        }

        int successCount = 0;
        int failCount = 0;
        int updateCount = 0;
        StringBuilder errorMsg = new StringBuilder();

        for (int i = 0; i < list.size(); i++) {
            StationContactVo vo = list.get(i);
            try {
                // 数据校验
                if (StringUtils.isBlank(vo.getLineName()) || StringUtils.isBlank(vo.getStationName())) {
                    failCount++;
                    errorMsg.append("第").append(i + 1).append("行：线别和车站名称不能为空<br/>");
                    continue;
                }

                // 检查是否已存在
                LambdaQueryWrapper<StationContact> queryWrapper = Wrappers.lambdaQuery();
                queryWrapper.eq(StationContact::getLineName, vo.getLineName())
                           .eq(StationContact::getStationName, vo.getStationName());
                StationContact existEntity = baseMapper.selectOne(queryWrapper);

                // 使用MapStruct自动转换器
                StationContactBo bo = StationContactMapper.StationContactMapStructMapper.INSTANCE.toBo(vo);

                if (existEntity != null && updateSupport) {
                    // 更新模式
                    bo.setId(existEntity.getId());
                    if (updateByBo(bo)) {
                        updateCount++;
                    } else {
                        failCount++;
                        errorMsg.append("第").append(i + 1).append("行：更新失败<br/>");
                    }
                } else if (existEntity == null) {
                    // 新增模式
                    if (insertByBo(bo)) {
                        successCount++;
                    } else {
                        failCount++;
                        errorMsg.append("第").append(i + 1).append("行：新增失败<br/>");
                    }
                } else {
                    // 存在且不允许更新
                    failCount++;
                    errorMsg.append("第").append(i + 1).append("行：数据已存在，请选择更新模式<br/>");
                }
            } catch (Exception e) {
                failCount++;
                errorMsg.append("第").append(i + 1).append("行：处理异常：").append(e.getMessage()).append("<br/>");
            }
        }

        // 构建返回消息
        StringBuilder result = new StringBuilder();
        result.append("导入完成！");
        result.append("新增 ").append(successCount).append(" 条，");
        result.append("更新 ").append(updateCount).append(" 条，");
        result.append("失败 ").append(failCount).append(" 条");

        if (failCount > 0) {
            result.append("<br/><br/>错误详情：<br/>").append(errorMsg.toString());
        }

        return result.toString();
    }
}