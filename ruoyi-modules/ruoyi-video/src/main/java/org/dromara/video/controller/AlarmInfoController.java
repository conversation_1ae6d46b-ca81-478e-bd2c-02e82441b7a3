package org.dromara.video.controller;

import java.util.List;

import cn.dev33.satoken.annotation.SaIgnore;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.video.domain.vo.AlarmInfoVo;
import org.dromara.video.domain.bo.AlarmInfoBo;
import org.dromara.video.service.IAlarmInfoService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 告警信息
 *
 * <AUTHOR>
 * @date 2025-09-05
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/video/alarmInfo")
public class AlarmInfoController extends BaseController {

    private final IAlarmInfoService alarmInfoService;

    /**
     * 查询告警信息列表
     */
    @SaCheckPermission("video:alarmInfo:list")
    @GetMapping("/list")
    public TableDataInfo<AlarmInfoVo> list(AlarmInfoBo bo, PageQuery pageQuery) {
        return alarmInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出告警信息列表
     */
    @SaCheckPermission("video:alarmInfo:export")
    @Log(title = "告警信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AlarmInfoBo bo, HttpServletResponse response) {
        List<AlarmInfoVo> list = alarmInfoService.queryList(bo);
        ExcelUtil.exportExcel(list, "告警信息", AlarmInfoVo.class, response);
    }

    /**
     * 获取告警信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("video:alarmInfo:query")
    @GetMapping("/{id}")
    public R<AlarmInfoVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable String id) {
        return R.ok(alarmInfoService.queryById(id));
    }

    /**
     * 新增告警信息
     */
    @SaCheckPermission("video:alarmInfo:add")
    @Log(title = "告警信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AlarmInfoBo bo) {
        return toAjax(alarmInfoService.insertByBo(bo));
    }

    /**
     * 修改告警信息
     */
    @SaCheckPermission("video:alarmInfo:edit")
    @Log(title = "告警信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AlarmInfoBo bo) {
        return toAjax(alarmInfoService.updateByBo(bo));
    }
    /**
     * 批量修改告警信息处理结果和处理意见
     */
    @SaCheckPermission("video:alarmInfo:edit")
    @Log(title = "告警信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/batch")
    public R<Void> batchEdit(@RequestBody AlarmInfoBo bo) {
        // 确保必要的字段存在
        if (bo.getIds() == null || bo.getIds().isEmpty()) {
            return R.fail("请选择要处理的告警信息");
        }
        return toAjax(alarmInfoService.updateBatchByIds(bo));
    }
    /**
     * 删除告警信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("video:alarmInfo:remove")
    @Log(title = "告警信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String[] ids) {
        return toAjax(alarmInfoService.deleteWithValidByIds(List.of(ids), true));
    }
}
