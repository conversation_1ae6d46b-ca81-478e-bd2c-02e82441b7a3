package org.dromara.video.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.video.domain.vo.DeviceDownloadVo;
import org.dromara.video.domain.bo.DeviceDownloadBo;
import org.dromara.video.service.IDeviceDownloadService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 视频下载记录
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/video/download")
public class DeviceDownloadController extends BaseController {

    private final IDeviceDownloadService deviceDownloadService;

    /**
     * 查询视频下载记录列表
     */
    @SaCheckPermission("video:download:list")
    @GetMapping("/list")
    public TableDataInfo<DeviceDownloadVo> list(DeviceDownloadBo bo, PageQuery pageQuery) {
        return deviceDownloadService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出视频下载记录列表
     */
    @SaCheckPermission("video:download:export")
    @Log(title = "视频下载记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(DeviceDownloadBo bo, HttpServletResponse response) {
        List<DeviceDownloadVo> list = deviceDownloadService.queryList(bo);
        ExcelUtil.exportExcel(list, "视频下载记录", DeviceDownloadVo.class, response);
    }

    /**
     * 获取视频下载记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("video:download:query")
    @GetMapping("/{id}")
    public R<DeviceDownloadVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(deviceDownloadService.queryById(id));
    }

    /**
     * 新增视频下载记录
     */
    @SaCheckPermission("video:download:add")
    @Log(title = "视频下载记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody DeviceDownloadBo bo) {
        return toAjax(deviceDownloadService.insertByBo(bo));
    }

    /**
     * 修改视频下载记录
     */
    @SaCheckPermission("video:download:edit")
    @Log(title = "视频下载记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody DeviceDownloadBo bo) {
        return toAjax(deviceDownloadService.updateByBo(bo));
    }

    /**
     * 删除视频下载记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("video:download:remove")
    @Log(title = "视频下载记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(deviceDownloadService.deleteWithValidByIds(List.of(ids), true));
    }
}
