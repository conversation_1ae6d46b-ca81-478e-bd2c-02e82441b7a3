package org.dromara.video.controller;

import java.util.*;
import java.util.stream.Collectors;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.lang.tree.Tree;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.excel.core.ExcelResult;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.system.domain.bo.SysDeptBo;
import org.dromara.system.service.ISysDeptService;
import org.dromara.video.domain.vo.DeviceInfoImportVo;
import org.dromara.video.listener.DeviceInfoImportListener;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.video.domain.vo.DeviceInfoVo;
import org.dromara.video.domain.bo.DeviceInfoBo;
import org.dromara.video.service.IDeviceInfoService;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 设备管理test
 *
 * <AUTHOR>
 * @date 2025-08-22
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/video/deviceInfo")
public class DeviceInfoController extends BaseController {

    private final IDeviceInfoService deviceInfoService;
    private final ISysDeptService deptService;

    /**
     * 启用设备
     */
    @Log(title = "启用设备", businessType = BusinessType.UPDATE)
    @PostMapping("/deploy/{deviceId}")
    public R<Void> deploy(@PathVariable String deviceId,@RequestHeader(name = "x-access-token", required = false) String token){
        Boolean result = deviceInfoService.deployDevice(deviceId, token);
        if (result) {
            return R.ok("设备启用成功");
        } else {
            return R.fail("设备启用失败");
        }
    }

    /**
     * 禁用设备
     */
    @Log(title = "禁用设备", businessType = BusinessType.UPDATE)
    @PostMapping("/undeploy/{deviceId}")
    public R<Void> undeploy(@PathVariable String deviceId,@RequestHeader(name = "x-access-token", required = false) String token){
        Boolean result = deviceInfoService.undeployDevice(deviceId, token);
        if (result) {
            return R.ok("设备禁用成功");
        } else {
            return R.fail("设备禁用失败");
        }
    }

    /**
     * 查询分组树
     */

    @GetMapping("/getGroupTree")
    public R<List<Tree<Long>>> getGroupTree() {
        // 构造部门查询参数
        SysDeptBo deptBo = new SysDeptBo();
        deptBo.setDeptCategory("1");
        return R.ok(deptService.selectDeptTreeList(deptBo));
    }

    /**
     * 查询设备目录树（带权限控制）
     */

    @GetMapping("/deviceDeptTree")
    public R<List<Tree<Long>>> getDeviceDeptTree(@RequestParam(required = false) String deviceName) {
        // 构造部门查询参数
        SysDeptBo deptBo = new SysDeptBo();
        // 获取设备数据并按部门ID分组
        DeviceInfoBo deviceBo = new DeviceInfoBo();
        // 获取部门树基础数据
        List<Tree<Long>> deptTree = deptService.selectDeptTreeList(deptBo);
        // 添加设备名称查询条件
        if (deviceName != null && !deviceName.isEmpty()) {
            deviceBo.setDeviceName(deviceName);
        }

        List<DeviceInfoVo> deviceList = deviceInfoService.queryList(deviceBo);

        // 创建部门ID到设备列表的映射，过滤掉org_id为null的设备
        Map<Long, List<DeviceInfoVo>> deptDeviceMap = new HashMap<>();
        for (DeviceInfoVo device : deviceList) {
            Long orgId = device.getOrgId();
            if (orgId != null) {
                deptDeviceMap.computeIfAbsent(orgId, k -> new ArrayList<>()).add(device);
            }
        }
        // 对每个部门的设备列表按指定顺序排序: online、offline、notActive
        for (List<DeviceInfoVo> devices : deptDeviceMap.values()) {
            devices.sort((d1, d2) -> {
                String status1 = d1.getOnlineStatus() != null ? d1.getOnlineStatus() : "";
                String status2 = d2.getOnlineStatus() != null ? d2.getOnlineStatus() : "";

                // 定义状态优先级，online(0) > offline(1) > notActive(2) > 其他(3)
                int priority1 = "online".equals(status1) ? 0 :
                    "offline".equals(status1) ? 1 :
                        "notActive".equals(status1) ? 2 : 3;

                int priority2 = "online".equals(status2) ? 0 :
                    "offline".equals(status2) ? 1 :
                        "notActive".equals(status2) ? 2 : 3;

                return Integer.compare(priority1, priority2);
            });
        }

        // 递归处理部门树，附加设备信息
        deptTree.forEach(node -> processTreeNode(node, deptDeviceMap));

        return R.ok(deptTree);
    }


    /**
     * 递归处理树节点，附加设备信息
     */
    private void processTreeNode(Tree<Long> node, Map<Long, List<DeviceInfoVo>> deptDeviceMap) {
        // 获取当前节点的设备列表 - 使用节点ID（dept_id）匹配设备的org_id
        List<DeviceInfoVo> devices = deptDeviceMap.getOrDefault(node.getId(), Collections.emptyList());

        // 创建设备子节点列表
        List<Tree<Long>> deviceNodes = new ArrayList<>();
        for (DeviceInfoVo device : devices) {
            Tree<Long> deviceNode = new Tree<>();
            // 使用设备ID作为节点ID，确保唯一性
            deviceNode.setId(Long.valueOf(device.getId().toString()));
            deviceNode.setName(device.getDeviceName());
            deviceNode.setParentId(node.getId());
            // 添加设备信息到extra属性中
            deviceNode.putExtra("deviceInfo", device);
            deviceNodes.add(deviceNode);
        }

        // 合并现有的子节点（部门节点）和设备节点
        List<Tree<Long>> children = new ArrayList<>();
        if (node.getChildren() != null) {
            children.addAll(node.getChildren());
        }
        children.addAll(deviceNodes);

        // 对子节点进行排序：有deviceInfo的节点排在前面，没有的排在后面
        children.sort((n1, n2) -> {
            boolean hasDeviceInfo1 = n1.containsKey("deviceInfo");
            boolean hasDeviceInfo2 = n2.containsKey("deviceInfo");

            // 如果第一个节点有设备信息，第二个没有，则第一个排在前面
            if (hasDeviceInfo1 && !hasDeviceInfo2) {
                return -1;
            }
            // 如果第二个节点有设备信息，第一个没有，则第二个排在前面
            if (!hasDeviceInfo1 && hasDeviceInfo2) {
                return 1;
            }
            // 如果都有或都没有设备信息，则保持原有顺序
            return 0;
        });
        // 设置标准children字段
        node.setChildren(children);

        // 递归处理子节点（仅处理部门节点，不处理设备节点）
        if (node.getChildren() != null && !node.getChildren().isEmpty()) {
            for (Tree<Long> child : node.getChildren()) {
                // 我们可以通过检查是否有deviceInfo属性来判断是否为设备节点
                if (!child.containsKey("deviceInfo")) {
                    processTreeNode(child, deptDeviceMap);
                }
            }
        }
    }

    /**
     * 更新设备在线状态
     */
    @PostMapping("/updateOnlineStatus")
    public R<Void> updateOnlineStatus(@RequestBody DeviceInfoBo bo,@RequestHeader(name = "x-access-token", required = false) String token) {
        Boolean result = deviceInfoService.updateOnlineStatus(bo, token);
        if (result) {
            return R.ok("更新在线状态成功");
        } else {
            return R.fail("更新在线状态失败");
        }
    }

    /**
     * 统计设备信息
     */
    @GetMapping("/statistics")
    public R<Map<String, Object>> getDeviceStatistics() {
        Map<String, Object> statistics = deviceInfoService.getDeviceStatistics();
        return R.ok(statistics);
    }

    /**
     * 查询设备管理列表
     */
    @SaCheckPermission("video:device:list")
    @GetMapping("/list")
    public TableDataInfo<DeviceInfoVo> list(DeviceInfoBo bo, PageQuery pageQuery ,@RequestHeader(name = "x-access-token", required = false) String token) {
        // 首先更新设备在线状态
        DeviceInfoBo queryBo = new DeviceInfoBo();
        deviceInfoService.updateOnlineStatus(queryBo, token);
        // 查询本地数据库获取设备列表
        TableDataInfo<DeviceInfoVo> result = deviceInfoService.queryPageList(bo, pageQuery);

        return result;
    }

    /**
     * 导出设备管理列表
     */
    @SaCheckPermission("video:deviceInfo:export")
    @Log(title = "设备管理test", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(DeviceInfoBo bo, HttpServletResponse response) {
        List<DeviceInfoVo> list = deviceInfoService.queryList(bo);
        ExcelUtil.exportExcel(list, "设备管理", DeviceInfoVo.class, response);
    }

    /**
     * 获取设备管理详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("video:deviceInfo:query")
    @GetMapping("/{id}")
    public R<DeviceInfoVo> getInfo(@NotNull(message = "主键不能为空")
                                   @PathVariable Long id) {
        return R.ok(deviceInfoService.queryById(id));
    }

    /**
     * 新增设备管理
     */
    @SaCheckPermission("video:deviceInfo:add")
    @Log(title = "设备管理test", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody DeviceInfoBo bo,
                       @RequestHeader(name = "x-access-token", required = false) String token) {
        Boolean result = deviceInfoService.addDeviceWithApi(bo, token);
        if (result) {
            return toAjax(true);
        } else {
            return R.fail("设备创建失败");
        }
    }

    /**
     * 修改设备管理
     */
    @SaCheckPermission("video:deviceInfo:edit")
    @Log(title = "设备管理test", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody DeviceInfoBo bo,
                        @RequestHeader(name = "x-access-token", required = false) String token) {
        Boolean result = deviceInfoService.editDeviceWithApi(bo, token);
        if (result) {
            return toAjax(true);
        } else {
            return R.fail("设备更新失败");
        }
    }

    /**
     * 删除设备管理
     */
    @SaCheckPermission("video:deviceInfo:remove")
    @Log(title = "设备管理test", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids,
                          @RequestHeader(name = "x-access-token", required = false) String token) {
        Boolean result = deviceInfoService.removeDevicesWithApi(ids, token);
        if (result) {
            return toAjax(true);
        } else {
            return R.fail("设备删除失败");
        }
    }

    /**
     * 导入数据
     *
     * @param file          导入文件
     * @param updateSupport 是否更新已存在数据
     */
    @Log(title = "设备管理", businessType = BusinessType.IMPORT)
    @PostMapping(value = "/importData", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<Void> importData(@RequestPart("file") MultipartFile file, boolean updateSupport) throws Exception {
        ExcelResult<DeviceInfoImportVo> result = ExcelUtil.importExcel(file.getInputStream(), DeviceInfoImportVo.class, new DeviceInfoImportListener(deviceInfoService, updateSupport));
        return R.ok(result.getAnalysis());
    }

    /**
     * 获取导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil.exportExcel(new ArrayList<>(), "设备数据", DeviceInfoImportVo.class, response);
    }
}
