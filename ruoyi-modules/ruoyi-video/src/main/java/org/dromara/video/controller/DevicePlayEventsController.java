package org.dromara.video.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.video.domain.vo.DevicePlayEventsVo;
import org.dromara.video.domain.bo.DevicePlayEventsBo;
import org.dromara.video.service.IDevicePlayEventsService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 设备播放事件明细
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/video/playEvents")
public class DevicePlayEventsController extends BaseController {

    private final IDevicePlayEventsService devicePlayEventsService;

    /**
     * 查询设备播放事件明细列表
     */
    @SaCheckPermission("video:playEvents:list")
    @GetMapping("/list")
    public TableDataInfo<DevicePlayEventsVo> list(DevicePlayEventsBo bo, PageQuery pageQuery) {
        return devicePlayEventsService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出设备播放事件明细列表
     */
    @SaCheckPermission("video:playEvents:export")
    @Log(title = "设备播放事件明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(DevicePlayEventsBo bo, HttpServletResponse response) {
        List<DevicePlayEventsVo> list = devicePlayEventsService.queryList(bo);
        ExcelUtil.exportExcel(list, "设备播放事件明细", DevicePlayEventsVo.class, response);
    }

    /**
     * 获取设备播放事件明细详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("video:playEvents:query")
    @GetMapping("/{id}")
    public R<DevicePlayEventsVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(devicePlayEventsService.queryById(id));
    }

    /**
     * 新增设备播放事件明细
     */
    @SaCheckPermission("video:playEvents:add")
    @Log(title = "设备播放事件明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody DevicePlayEventsBo bo) {
        return toAjax(devicePlayEventsService.insertByBo(bo));
    }

    /**
     * 修改设备播放事件明细
     */
    @SaCheckPermission("video:playEvents:edit")
    @Log(title = "设备播放事件明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody DevicePlayEventsBo bo) {
        return toAjax(devicePlayEventsService.updateByBo(bo));
    }

    /**
     * 删除设备播放事件明细
     *
     * @param ids 主键串
     */
    @SaCheckPermission("video:playEvents:remove")
    @Log(title = "设备播放事件明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(devicePlayEventsService.deleteWithValidByIds(List.of(ids), true));
    }
}
