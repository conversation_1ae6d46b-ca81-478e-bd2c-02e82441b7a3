package org.dromara.video.controller;

import java.util.List;

import cn.dev33.satoken.annotation.SaIgnore;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.video.domain.vo.DeviceRecordVo;
import org.dromara.video.domain.bo.DeviceRecordBo;
import org.dromara.video.service.IDeviceRecordService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 云端视频文件回放
 *
 * <AUTHOR>
 * @date 2025-09-17
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/video/record")
public class DeviceRecordController extends BaseController {
    static {
        System.out.println("DeviceRecordController class loaded");
    }

    private final IDeviceRecordService deviceRecordService;
    /**
     * 测试端点
     */
    @GetMapping("/test")
    public R<String> test() {
        System.out.println("DeviceRecordController.test() method called");
        return R.ok("Test successful");
    }
    /**
     * 查询云端视频文件回放列表
     */
    @SaCheckPermission("video:record:list")
    @GetMapping("/list")
    public TableDataInfo<DeviceRecordVo> list(DeviceRecordBo bo, PageQuery pageQuery) {
        return deviceRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出云端视频文件回放列表
     */
    @SaCheckPermission("video:record:export")
    @Log(title = "云端视频文件回放", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(DeviceRecordBo bo, HttpServletResponse response) {
        List<DeviceRecordVo> list = deviceRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "云端视频文件回放", DeviceRecordVo.class, response);
    }

    /**
     * 获取云端视频文件回放详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("video:record:query")
    @GetMapping("/{id}")
    public R<DeviceRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(deviceRecordService.queryById(id));
    }

    /**
     * 新增云端视频文件回放
     */
    @SaCheckPermission("video:record:add")
    @Log(title = "云端视频文件回放", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody DeviceRecordBo bo) {
        return toAjax(deviceRecordService.insertByBo(bo));
    }

    /**
     * 修改云端视频文件回放
     */
    @SaCheckPermission("video:record:edit")
    @Log(title = "云端视频文件回放", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody DeviceRecordBo bo) {
        return toAjax(deviceRecordService.updateByBo(bo));
    }

    /**
     * 删除云端视频文件回放
     *
     * @param ids 主键串
     */
    @SaCheckPermission("video:record:remove")
    @Log(title = "云端视频文件回放", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(deviceRecordService.deleteWithValidByIds(List.of(ids), true));
    }
}
