package org.dromara.video.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import org.dromara.common.core.domain.R;
import org.dromara.common.web.core.BaseController;
import org.dromara.video.service.VideoPlatformService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@SaIgnore
@RestController
@RequestMapping("/video/platform")
public class VideoPlatformController extends BaseController {
    @Autowired
    private VideoPlatformService videoPlatformService;
    @GetMapping("/token")
    public R<String> refreshPlatformToken(){
        return R.ok("操作成功", videoPlatformService.refreshToken());
    }

}
