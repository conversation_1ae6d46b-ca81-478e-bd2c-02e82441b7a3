package org.dromara.video.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.translation.annotation.Translation;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.common.translation.constant.TransConstant;

import java.io.Serial;

/**
 * 告警信息对象 alarm_info
 *
 * <AUTHOR>
 * @date 2025-09-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("alarm_info")
public class AlarmInfo extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID（自增）
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 设备编码（关联设备信息）
     */
    private String deviceId;

    /**
     * 告警类型
     */
    private String alarmType;

    /**
     * 告警时间
     */
    private Date alarmTime;

    /**
     * 处理意见
     */
    private String description;

    /**
     * 来源(0:AI识别 1:人工上报)
     */
    private String source;

    /**
     * 处理状态(0:未处理 1:已处理)
     */
    private String status;

    /**
     * 告警图片地址
     */
    private String image;

    /**
     * 备注说明
     */
    private String remark;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 组织名称
     */
    private String orgName;

    /**
     * 组织ID
     */
    private Long orgId;

    /**
     * 报警等级
     */
    private String level;
    /**
     * 处理时间
     */
    private Date processTime;

    /**
     * 处理人
     */
    private String processBy;



}
