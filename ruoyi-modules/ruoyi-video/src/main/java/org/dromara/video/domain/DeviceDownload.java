package org.dromara.video.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 视频下载记录对象 device_download
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("device_download")
public class DeviceDownload extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 文件唯一标识
     */
    private Long fileId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件存储路径
     */
    private String fileUrl;

    /**
     * 下载次数
     */
    private Long downloadTimes;

    /**
     * 文件状态（0-未处理，1-下载成功 ）
     */
    private Long fileState;

    /**
     * 备注说明
     */
    private String remark;


}
