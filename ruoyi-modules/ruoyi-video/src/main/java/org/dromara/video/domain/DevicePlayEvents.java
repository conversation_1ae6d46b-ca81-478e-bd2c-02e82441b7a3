package org.dromara.video.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 设备播放事件明细对象 device_play_events
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("device_play_events")
public class DevicePlayEvents extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 设备编码
     */
    private String deviceId;

    /**
     * 查看用户ID
     */
    private Long userId;

    /**
     * 查看用户名称
     */
    private String userName;

    /**
     * 开始播放时间
     */
    private Date startTime;

    /**
     * 结束播放时间
     */
    private Date endTime;

    /**
     * 播放时长(秒)
     */
    private Long duration;

    /**
     * 事件日期(用于分区和快速查询)
     */
    private Date eventDate;

    /**
     * 备注说明
     */
    private String remark;


}
