package org.dromara.video.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 云端视频文件回放对象 device_record
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("device_record")
public class DeviceRecord extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 设备编码
     */
    private String deviceId;

    /**
     * 拍摄时间
     */
    private Date takeTime;

    /**
     * 上传时间
     */
    private Date recordTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 文件地址
     */
    private String fileUrl;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 文件大小
     */
    private String fileType;

    /**
     * 文件标签
     */
    private String fileTags;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 文件时长
     */
    private Long fileTime;

    /**
     * 作业计划ID
     */
    private Long panId;

    /**
     * 播放次数
     */
    private Long playCount;

    /**
     * 播放记录ID
     */
    private Long videoRecordId;


}
