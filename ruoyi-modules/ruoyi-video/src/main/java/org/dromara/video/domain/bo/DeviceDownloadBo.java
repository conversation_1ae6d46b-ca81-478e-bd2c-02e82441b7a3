package org.dromara.video.domain.bo;

import org.dromara.video.domain.DeviceDownload;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 视频下载记录业务对象 device_download
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = DeviceDownload.class, reverseConvertGenerate = false)
public class DeviceDownloadBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 文件唯一标识
     */
    @NotNull(message = "文件唯一标识不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long fileId;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 文件名
     */
    @NotBlank(message = "文件名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fileName;

    /**
     * 文件存储路径
     */
    @NotBlank(message = "文件存储路径不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fileUrl;

    /**
     * 下载次数
     */
    private Long downloadTimes;

    /**
     * 文件状态（0-未处理，1-下载成功 ）
     */
    private Long fileState;

    /**
     * 备注说明
     */
    private String remark;


}
