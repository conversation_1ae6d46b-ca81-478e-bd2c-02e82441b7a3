package org.dromara.video.domain.bo;

import org.dromara.video.domain.DeviceInfo;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 设备管理test业务对象 device_info
 *
 * <AUTHOR>
 * @date 2025-08-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = DeviceInfo.class, reverseConvertGenerate = false)
public class DeviceInfoBo extends BaseEntity {

    /**
     * 主键ID（自增）
     */
    @NotNull(message = "主键ID（自增）不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 国标编号（唯一标识设备的国家标准编号）
     */
    private String gbId;

    /**
     * 设备编码（唯一索引，业务唯一标识）
     */
    private String deviceId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备登录账户
     */
    private String deviceUsername;

    /**
     * 设备登录密码（加密存储）
     */
    private String devicePassword;

    /**
     * 设备类型（0摄像头 1:执法记录仪）配置字典管理
     */
    private Long deviceType;

    /**
     * 流传输协议（TCP，UDP）
     */
    private String deviceTrans;

    /**
     * 设备厂商
     */
    private String deviceVendor;

    /**
     * 设备型号
     */
    private String deviceModel;

    /**
     * 设备IP地址
     */
    private String deviceIp;

    /**
     * 设备端口
     */
    private Long devicePort;

    /**
     * 设备固件版本
     */
    private String deviceVersion;

    /**
     * 设备标识（通过字典管理）
     */
    private Long deviceTags;

    /**
     * 接入账号
     */
    private String accessAccount;

    /**
     * 接入密码（加密存储）
     */
    private String accessPassword;

    /**
     * 接入协议 (GB/T 282181、ONVIF)
     */
    private String accessProtocol;

    /**
     * 接入SIP地址
     */
    private String sipAddress;

    /**
     * 注册时间
     */
    private Date registerTime;

    /**
     * 注册过期时间
     */
    private Date expireTime;

    /**
     * 设备在线状态（0:离线 1:在线）
     */
    private String onlineStatus;

    /**
     * 启用状态（0:禁用 1:启用，默认1）
     */
    private String enableStatus;

    /**
     * 单位名称
     */
    private String deptName;

    /**
     * 所属组织名称
     */
    private String orgName;

    /**
     * 单位ID
     */
    private Long deptId;

    /**
     * 位置描述
     */
    private String location;

    /**
     * 经度
     */
    private Long longitude;

    /**
     * 纬度
     */
    private Long latitude;

    /**
     * 所属组织（ID关联单位目录树）
     */
    private Long orgId;

    /**
     * 备注说明
     */
    private String remark;

    /**
     * 视频通道ID
     */
    private String channelId;
    /**
     * 是否为组织设备
     */
    private String isOrg;

    /**
     * 是否已绑定
     */
    private String isBound;
    /**
     * 设备IMEI
     */
    private String deviceImei;
    /**
     * 多个单位名称，用逗号分隔
     */
    private String deptNames;
    /**
     * 多个组织名称，用逗号分隔
     */
    private String orgNames;

}
