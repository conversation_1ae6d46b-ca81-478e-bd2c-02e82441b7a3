package org.dromara.video.domain.bo;

import org.dromara.video.domain.DevicePlayEvents;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 设备播放事件明细业务对象 device_play_events
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = DevicePlayEvents.class, reverseConvertGenerate = false)
public class DevicePlayEventsBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 设备编码
     */
    @NotBlank(message = "设备编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String deviceId;

    /**
     * 查看用户ID
     */
    @NotNull(message = "查看用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 查看用户名称
     */
    private String userName;

    /**
     * 开始播放时间
     */
    @NotNull(message = "开始播放时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date startTime;

    /**
     * 结束播放时间
     */
    private Date endTime;

    /**
     * 播放时长(秒)
     */
    private Long duration;

    /**
     * 事件日期(用于分区和快速查询)
     */
    @NotNull(message = "事件日期(用于分区和快速查询)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date eventDate;

    /**
     * 备注说明
     */
    private String remark;


}
