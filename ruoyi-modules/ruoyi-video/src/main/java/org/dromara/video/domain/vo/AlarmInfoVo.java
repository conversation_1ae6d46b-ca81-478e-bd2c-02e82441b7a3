package org.dromara.video.domain.vo;

import org.dromara.common.translation.annotation.Translation;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.common.translation.constant.TransConstant;
import org.dromara.video.domain.AlarmInfo;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 告警信息视图对象 alarm_info
 *
 * <AUTHOR>
 * @date 2025-09-05
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AlarmInfo.class)
public class AlarmInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID（自增）
     */
    @ExcelProperty(value = "主键ID", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "自=增")
    private Long id;

    /**
     * 设备编码（关联设备信息）
     */
    @ExcelProperty(value = "设备编码", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "关=联设备信息")
    private String deviceId;

    /**
     * 告警类型
     */
    @ExcelProperty(value = "告警类型")
    private String alarmType;

    /**
     * 告警时间
     */
    @ExcelProperty(value = "告警时间")
    private Date alarmTime;

    /**
     * 处理意见
     */
    @ExcelProperty(value = "处理意见")
    private String description;

    /**
     * 来源(0:AI识别 1:人工上报)
     */
    @ExcelProperty(value = "来源(0:AI识别 1:人工上报)")
    private String source;

    /**
     * 处理状态(0:未处理 1:已处理)
     */
    @ExcelProperty(value = "处理状态(0:未处理 1:已处理)")
    private String status;

    /**
     * 告警图片地址
     */
    @ExcelProperty(value = "告警图片地址")
    private String image;

    /**
     * 告警图片地址Url
     */
    @Translation(type = TransConstant.OSS_ID_TO_URL, mapper = "image")
    private String imageUrl;
    /**
     * 备注说明
     */
    @ExcelProperty(value = "备注说明")
    private String remark;

    /**
     * 部门名称
     */
    @ExcelProperty(value = "部门名称")
    private String deptName;

    /**
     * 部门ID
     */
    @ExcelProperty(value = "部门ID")
    private Long deptId;

    /**
     * 组织名称
     */
    @ExcelProperty(value = "组织名称")
    private String orgName;

    /**
     * 组织ID
     */
    @ExcelProperty(value = "组织ID")
    private Long orgId;

    /**
     * 报警等级
     */
    @ExcelProperty(value = "报警等级")
    private String level;

    /**
     * 处理时间
     */
    @ExcelProperty(value = "处理时间")
    private Date processTime;

    /**
     * 处理人
     */
    @ExcelProperty(value = "处理人")
    private String processBy;



}
