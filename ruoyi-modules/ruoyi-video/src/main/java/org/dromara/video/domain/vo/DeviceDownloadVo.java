package org.dromara.video.domain.vo;

import org.dromara.video.domain.DeviceDownload;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 视频下载记录视图对象 device_download
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = DeviceDownload.class)
public class DeviceDownloadVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 文件唯一标识
     */
    @ExcelProperty(value = "文件唯一标识")
    private Long fileId;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 用户名称
     */
    @ExcelProperty(value = "用户名称")
    private String userName;

    /**
     * 文件名
     */
    @ExcelProperty(value = "文件名")
    private String fileName;

    /**
     * 文件存储路径
     */
    @ExcelProperty(value = "文件存储路径")
    private String fileUrl;

    /**
     * 下载次数
     */
    @ExcelProperty(value = "下载次数")
    private Long downloadTimes;

    /**
     * 文件状态（0-未处理，1-下载成功 ）
     */
    @ExcelProperty(value = "文件状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=-未处理，1-下载成功")
    private Long fileState;

    /**
     * 备注说明
     */
    @ExcelProperty(value = "备注说明")
    private String remark;


}
