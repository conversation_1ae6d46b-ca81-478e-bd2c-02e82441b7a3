package org.dromara.video.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 设备管理导入视图对象
 *
 * <AUTHOR> Li
 * @date 2025-09-11
 */
@Data
public class DeviceInfoImportVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 国标编号（唯一标识设备的国家标准编号）
     */
    @ExcelProperty(value = "国标编号")
    private String gbId;

    /**
     * 设备编码（唯一索引，业务唯一标识）
     */
    @ExcelProperty(value = "设备编码")
    private String deviceId;

    /**
     * 设备名称
     */
    @ExcelProperty(value = "设备名称")
    private String deviceName;

    /**
     * 设备IMEI
     */
    @ExcelProperty(value = "设备IMEI")
    private String deviceImei;

    /**
     * 设备类型（0摄像头 1:执法记录仪）配置字典管理
     */
    @ExcelProperty(value = "设备类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "device_type")
    private Long deviceType;

    /**
     * 流传输协议（TCP，UDP）
     */
    @ExcelProperty(value = "流传输协议", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "device_trans")
    private String deviceTrans;


    /**
     * 接入密码（加密存储）
     */
    @ExcelProperty(value = "接入密码")
    private String accessPassword;

    /**
     * 接入协议 (GB/T 282181、ONVIF)
     */
    @ExcelProperty(value = "接入协议", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_provider_options")
    private String accessProtocol;

    /**
     * 单位名称
     */
    @ExcelProperty(value = "单位名称")
    private String deptName;
    /**
     * 所属组织名称
     */
    @ExcelProperty(value = "分组名称")
    private String orgName;
    /**
     * 单位ID
     */
    @ExcelProperty(value = "单位ID")
    private Long deptId;
    /**
     * 所属组织（ID关联单位目录树）
     */
    @ExcelProperty(value = "分组ID")
    private Long orgId;
    /**
     * 视频通道ID
     */
    @ExcelProperty(value = "视频通道ID")
    private String channelId;

    /**
     * 是否为组织设备
     */
    @ExcelProperty(value = "是否外单位设备", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "is_org")
    private String isOrg;

    /**
     * 是否已绑定
     */
    @ExcelProperty(value = "是否绑定", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "is_bound")
    private String isBound;
    /**
     * 设备厂商
     */
    @ExcelProperty(value = "设备厂商")
    private String deviceVendor;
    /**
     * 设备标识（通过字典管理）
     */
    @ExcelProperty(value = "设备标识", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "device_tags")
    private Long deviceTags;

}
