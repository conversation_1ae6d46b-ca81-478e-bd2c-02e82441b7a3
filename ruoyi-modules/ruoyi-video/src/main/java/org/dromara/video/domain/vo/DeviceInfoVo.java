package org.dromara.video.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.video.domain.DeviceInfo;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 设备管理test视图对象 device_info
 *
 * <AUTHOR>
 * @date 2025-08-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = DeviceInfo.class)
public class DeviceInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID（自增）
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 国标编号（唯一标识设备的国家标准编号）
     */
    @ExcelProperty(value = "国标编号")
    private String gbId;

    /**
     * 设备编码（唯一索引，业务唯一标识）
     */
    @ExcelProperty(value = "设备编码")
    private String deviceId;

    /**
     * 设备名称
     */
    @ExcelProperty(value = "设备名称")
    private String deviceName;

    /**
     * 设备登录账户
     */
    @ExcelProperty(value = "设备登录账户")
    private String deviceUsername;

    /**
     * 设备登录密码（加密存储）
     */
    @ExcelProperty(value = "设备登录密码")
    private String devicePassword;

    /**
     * 设备类型（0摄像头 1:执法记录仪）配置字典管理
     */
    @ExcelProperty(value = "设备类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "device_type")
    private Long deviceType;

    /**
     * 流传输协议（TCP，UDP）
     */
    @ExcelProperty(value = "流传输协议", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "device_trans")
    private String deviceTrans;

    /**
     * 设备厂商
     */
    @ExcelProperty(value = "设备厂商")
    private String deviceVendor;

    /**
     * 设备型号
     */
    @ExcelProperty(value = "设备型号")
    private String deviceModel;

    /**
     * 设备IP地址
     */
    @ExcelProperty(value = "设备IP地址")
    private String deviceIp;

    /**
     * 设备端口
     */
    @ExcelProperty(value = "设备端口")
    private Long devicePort;

    /**
     * 设备固件版本
     */
    @ExcelProperty(value = "设备固件版本")
    private String deviceVersion;

    /**
     * 设备标识（通过字典管理）
     */
    @ExcelProperty(value = "设备标识", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "device_tags")
    private Long deviceTags;

    /**
     * 接入账号
     */
    @ExcelProperty(value = "接入账号")
    private String accessAccount;

    /**
     * 接入密码（加密存储）
     */
    @ExcelProperty(value = "接入密码")
    private String accessPassword;

    /**
     * 接入协议 (GB/T 282181、ONVIF)
     */
    @ExcelProperty(value = "接入协议 (GB/T 282181、ONVIF)", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_provider_options")
    private String accessProtocol;

    /**
     * 接入SIP地址
     */
    @ExcelProperty(value = "接入SIP地址")
    private String sipAddress;

    /**
     * 注册时间
     */
    @ExcelProperty(value = "注册时间")
    private Date registerTime;

    /**
     * 注册过期时间
     */
    @ExcelProperty(value = "注册过期时间")
    private Date expireTime;

    /**
     * 设备在线状态（0:离线 1:在线）
     */
    @ExcelProperty(value = "设备在线状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "iot_device_state")
    private String onlineStatus;

    /**
     * 启用状态（0:禁用 1:启用，默认1）
     */
    @ExcelProperty(value = "启用状态")
    private String enableStatus;

    /**
     * 单位名称
     */
    @ExcelProperty(value = "单位名称")
    private String deptName;

    /**
     * 所属组织名称
     */
    @ExcelProperty(value = "所属组织名称")
    private String orgName;

    /**
     * 单位ID
     */
    @ExcelProperty(value = "单位ID")
    private Long deptId;

    /**
     * 位置描述
     */
    @ExcelProperty(value = "位置描述")
    private String location;

    /**
     * 经度
     */
    @ExcelProperty(value = "经度")
    private Long longitude;

    /**
     * 纬度
     */
    @ExcelProperty(value = "纬度")
    private Long latitude;

    /**
     * 所属组织（ID关联单位目录树）
     */
    @ExcelProperty(value = "所属组织")
    private Long orgId;

    /**
     * 备注说明
     */
    @ExcelProperty(value = "备注说明")
    private String remark;

    /**
     * 视频通道ID
     */
    @ExcelProperty(value = "视频通道ID")
    private String channelId;
    /**
     * 是否为组织设备
     */
    @ExcelProperty(value = "是否为组织设备", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "is_org")
    private String isOrg;

    /**
     * 是否已绑定
     */
    @ExcelProperty(value = "是否绑定", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "is_bound")
    private String isBound;
    /**
     * 设备IMEI
     */
    @ExcelProperty(value = "设备IMEI")
    private String deviceImei;

}
