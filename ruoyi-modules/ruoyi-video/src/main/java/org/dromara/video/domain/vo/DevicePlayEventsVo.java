package org.dromara.video.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.video.domain.DevicePlayEvents;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 设备播放事件明细视图对象 device_play_events
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = DevicePlayEvents.class)
public class DevicePlayEventsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 设备编码
     */
    @ExcelProperty(value = "设备编码")
    private String deviceId;

    /**
     * 查看用户ID
     */
    @ExcelProperty(value = "查看用户ID")
    private Long userId;

    /**
     * 查看用户名称
     */
    @ExcelProperty(value = "查看用户名称")
    private String userName;

    /**
     * 开始播放时间
     */
    @ExcelProperty(value = "开始播放时间")
    private Date startTime;

    /**
     * 结束播放时间
     */
    @ExcelProperty(value = "结束播放时间")
    private Date endTime;

    /**
     * 播放时长(秒)
     */
    @ExcelProperty(value = "播放时长(秒)")
    private Long duration;

    /**
     * 事件日期(用于分区和快速查询)
     */
    @ExcelProperty(value = "事件日期(用于分区和快速查询)")
    private Date eventDate;

    /**
     * 备注说明
     */
    @ExcelProperty(value = "备注说明")
    private String remark;


}
