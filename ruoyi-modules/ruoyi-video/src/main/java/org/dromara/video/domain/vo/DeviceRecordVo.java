package org.dromara.video.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.video.domain.DeviceRecord;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 云端视频文件回放视图对象 device_record
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = DeviceRecord.class)
public class DeviceRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 设备编码
     */
    @ExcelProperty(value = "设备编码")
    private String deviceId;

    /**
     * 拍摄时间
     */
    @ExcelProperty(value = "拍摄时间")
    private Date takeTime;

    /**
     * 上传时间
     */
    @ExcelProperty(value = "上传时间")
    private Date recordTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 文件地址
     */
    @ExcelProperty(value = "文件地址")
    private String fileUrl;

    /**
     * 设备名称
     */
    @ExcelProperty(value = "设备名称")
    private String deviceName;

    /**
     * 部门名称
     */
    @ExcelProperty(value = "部门名称")
    private String deptName;

    /**
     * 部门ID
     */
    @ExcelProperty(value = "部门ID")
    private String deptId;

    /**
     * 文件大小
     */
    @ExcelProperty(value = "文件大小")
    private String fileType;

    /**
     * 文件标签
     */
    @ExcelProperty(value = "文件标签")
    private String fileTags;

    /**
     * 文件大小
     */
    @ExcelProperty(value = "文件大小")
    private Long fileSize;

    /**
     * 文件时长
     */
    @ExcelProperty(value = "文件时长")
    private Long fileTime;

    /**
     * 作业计划ID
     */
    @ExcelProperty(value = "作业计划ID")
    private Long panId;

    /**
     * 播放次数
     */
    @ExcelProperty(value = "播放次数")
    private Long playCount;

    /**
     * 播放记录ID
     */
    @ExcelProperty(value = "播放记录ID")
    private Long videoRecordId;


}
