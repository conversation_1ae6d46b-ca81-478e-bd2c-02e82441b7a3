package org.dromara.video.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HtmlUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.service.DictService;
import org.dromara.common.core.utils.StreamUtils;
import org.dromara.common.core.utils.ValidatorUtils;
import org.dromara.common.excel.core.ExcelListener;
import org.dromara.common.excel.core.ExcelResult;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.video.domain.bo.DeviceInfoBo;
import org.dromara.video.domain.vo.DeviceInfoImportVo;
import org.dromara.video.domain.vo.DeviceInfoVo;
import org.dromara.video.service.IDeviceInfoService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 设备管理自定义导入
 *
 * <AUTHOR> Li
 */
@Slf4j
public class DeviceInfoImportListener extends AnalysisEventListener<DeviceInfoImportVo> implements ExcelListener<DeviceInfoImportVo> {

    private final IDeviceInfoService deviceInfoService;

    private final Boolean isUpdateSupport;

    private final Long operUserId;

    @Autowired
    private DictService dictService;

    private int successNum = 0;
    private int failureNum = 0;
    private final StringBuilder successMsg = new StringBuilder();
    private final StringBuilder failureMsg = new StringBuilder();

    public DeviceInfoImportListener(IDeviceInfoService deviceInfoService, Boolean isUpdateSupport) {
        this.deviceInfoService = deviceInfoService;
        this.isUpdateSupport = isUpdateSupport;
        this.operUserId = LoginHelper.getUserId();
    }

    @Override
    public void invoke(DeviceInfoImportVo deviceInfoVo, AnalysisContext context) {
        DeviceInfoVo deviceInfo = this.deviceInfoService.queryByDeviceId(deviceInfoVo.getDeviceId());
        try {
            // 验证是否存在这个设备
            if (ObjectUtil.isNull(deviceInfo)) {
                DeviceInfoBo device = BeanUtil.toBean(deviceInfoVo, DeviceInfoBo.class);
                ValidatorUtils.validate(device);
                device.setCreateBy(operUserId);
                // 调用包含API调用的新增方法
                deviceInfoService.addDeviceWithApi(device, null);
                successNum++;
                successMsg.append("<br/>").append(successNum).append("、设备 ").append(device.getDeviceName()).append(" 导入成功");
            } else if (isUpdateSupport) {
                Long id = deviceInfo.getId();
                DeviceInfoBo device = BeanUtil.toBean(deviceInfoVo, DeviceInfoBo.class);
                device.setId(id);
                ValidatorUtils.validate(device);
                device.setUpdateBy(operUserId);
                deviceInfoService.editDeviceWithApi(device, null);
                successNum++;
                successMsg.append("<br/>").append(successNum).append("、设备 ").append(device.getDeviceName()).append(" 更新成功");
            } else {
                failureNum++;
                failureMsg.append("<br/>").append(failureNum).append("、设备 ").append(deviceInfo.getDeviceName()).append(" 已存在");
            }
        } catch (Exception e) {
            failureNum++;
            String msg = "<br/>" + failureNum + "、设备 " + HtmlUtil.cleanHtmlTag(deviceInfoVo.getDeviceName()) + " 导入失败：";
            String message = e.getMessage();
            if (e instanceof ConstraintViolationException cvException) {
                message = StreamUtils.join(cvException.getConstraintViolations(), ConstraintViolation::getMessage, ", ");
            }
            failureMsg.append(msg).append(message);
            log.error(msg, e);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {

    }

    @Override
    public ExcelResult<DeviceInfoImportVo> getExcelResult() {
        return new ExcelResult<>() {

            @Override
            public String getAnalysis() {
                if (failureNum > 0) {
                    failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
                    throw new ServiceException(failureMsg.toString());
                } else {
                    successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
                }
                return successMsg.toString();
            }

            @Override
            public List<DeviceInfoImportVo> getList() {
                return null;
            }

            @Override
            public List<String> getErrorList() {
                return null;
            }
        };
    }
}
