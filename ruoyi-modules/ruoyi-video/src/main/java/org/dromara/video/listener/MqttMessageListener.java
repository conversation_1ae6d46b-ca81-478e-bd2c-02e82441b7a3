package org.dromara.video.listener;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.web.core.BaseController;
import org.dromara.video.domain.AlarmInfo;
import org.dromara.video.domain.DeviceInfo;
import org.dromara.video.domain.vo.DeviceInfoVo;
import org.dromara.video.service.IDeviceInfoService;
import org.dromara.video.service.IAlarmInfoService;
import org.dromara.video.domain.bo.DeviceInfoBo;
import org.dromara.video.mapper.DeviceInfoMapper;
import org.dromara.video.mapper.AlarmInfoMapper;
import org.dromara.video.service.VideoPlatformService;
import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

@Slf4j
@Service
public class MqttMessageListener extends BaseController {
    private MqttClient client;
    @Autowired
    private IDeviceInfoService deviceInfoService;
    @Autowired
    private DeviceInfoMapper deviceInfoMapper;
    @Autowired
    private AlarmInfoMapper alarmInfoMapper;

    @Value("${mqtt.broker}")
    private String broker;

    @Value("${mqtt.client-id}")
    private String clientId;

    @Value("${mqtt.username}")
    private String username;

    @Value("${mqtt.password}")
    private String password;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private volatile boolean initialized = false;

    @PostConstruct
    public void connect() {
        try {
            MemoryPersistence persistence = new MemoryPersistence();
            // 创建MQTT客户端
            client = new MqttClient(broker, clientId, persistence);
            MqttConnectOptions options = new MqttConnectOptions();
            options.setCleanSession(false);
            options.setUserName(username);
            options.setPassword(password.toCharArray());
            options.setConnectionTimeout(30); // 连接超时30秒
            options.setKeepAliveInterval(60); // 心跳间隔60秒
            options.setAutomaticReconnect(true); // 启用自动重连

            // 设置回调
            client.setCallback(new MqttCallback() {
                @Override
                public void connectionLost(Throwable cause) {
                    log.warn("📡 MQTT connection lost: {}", cause.getMessage());
                }

                @Override
                public void messageArrived(String topic, MqttMessage message) throws Exception {
                    handleMessage(topic, message);
                }

                @Override
                public void deliveryComplete(IMqttDeliveryToken token) {
                    // 消息发送完成
                }
            });

            client.connect(options);
            log.info("✅ Successfully connected to MQTT broker");

        } catch (MqttException e) {
            log.error("❌ Failed to connect to MQTT broker: {}", e.getMessage(), e);
            log.error("🔧 Application will continue to run without MQTT connectivity");
        }
    }

    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        // ApplicationReadyEvent在应用完全启动后触发，此时Web上下文已准备就绪
        log.info("🚀 Application fully started, initializing MQTT subscriptions in web context");
        initializeSubscriptions();
    }

    private void initializeSubscriptions() {
        if (!initialized) {
            try {
                subscribeToTopics();
                initialized = true;
                log.info("✅ MQTT subscriptions initialized successfully in web context");
            } catch (Exception e) {
                log.error("❌ Failed to initialize MQTT subscriptions: {}", e.getMessage(), e);
            }
        }
    }

    private void handleMessage(String topic, MqttMessage message) {
        try {
            String payload = new String(message.getPayload());
            log.info("📩 Received MQTT message:");
            log.info("   Topic: {}", topic);
            log.info("   Payload: {}", payload);
            log.info("   QoS: {}", message.getQos());
            log.info("   Retained: {}", message.isRetained());

            // 解析JSON消息
            Map<String, Object> alertData = objectMapper.readValue(payload, Map.class);
            log.info("✅ Successfully parsed MQTT message: {}", alertData);

            // 可以在这里添加更多的消息处理逻辑
            processAlertMessage(alertData, topic);

        } catch (Exception e) {
            log.error("❌ Error processing MQTT message: {}", e.getMessage(), e);
        }
    }

    private void processAlertMessage(Map<String, Object> alertData, String topic) {
        try {
            log.info("⚙️ Processing alert message from topic: {}", topic);

            // 提取关键信息
            String deviceId = (String) alertData.get("deviceId");
            String eventType = (String) alertData.get("eventType");
            String eventState = (String) alertData.get("eventState");
            String channelName = (String) alertData.get("channelName");
            String timestamp = (String) alertData.get("timestamp");
            String imageUrl = (String) alertData.get("image");

            log.info("📊 Alert Details:");
            log.info("   Device ID: {}", deviceId);
            log.info("   Event Type: {}", eventType);
            log.info("   Event State: {}", eventState);
            log.info("   Channel Name: {}", channelName);
            log.info("   Timestamp: {}", timestamp);
            log.info("   Image URL: {}", imageUrl);

            // 可以在这里添加更多的业务逻辑处理
            // 例如保存到数据库、触发其他服务等
            handleAlertEvent(deviceId, eventType, eventState, channelName, timestamp, imageUrl);

        } catch (Exception e) {
            log.error("❌ Error processing alert message: {}", e.getMessage(), e);
        }
    }

    private void handleAlertEvent(String deviceId, String eventType, String eventState,
                                  String channelName, String timestamp, String imageUrl) {
        try {
            log.info("💾 Handling alert event for device: {}", deviceId);

            // 根据事件类型和状态进行不同的处理
            if ("duration".equals(eventType) && "active".equals(eventState)) {
                log.info("🔔 Duration alert is active for device: {}", deviceId);
                // 处理持续时间告警激活事件
            } else if ("duration".equals(eventType) && "inactive".equals(eventState)) {
                log.info("🔔 Duration alert is inactive for device: {}", deviceId);
                // 处理持续时间告警解除事件
            }

            // 可以在这里添加保存到数据库的逻辑
            // 创建告警信息实体
            AlarmInfo alarmInfo = new AlarmInfo();
            alarmInfo.setDeviceId(deviceId);
            // 设置告警类型为1（根据要求设置为固定值1）
            alarmInfo.setAlarmType("1");

            // 设置告警时间
            if (timestamp != null && !timestamp.isEmpty()) {
                try {
                    alarmInfo.setAlarmTime(new Date(Long.parseLong(timestamp)));
                } catch (NumberFormatException e) {
                    log.warn("⚠️ Invalid timestamp format: {}", timestamp);
                }
            }

            // 通过设备ID查找设备信息
            DeviceInfoVo deviceInfo = deviceInfoService.queryByDeviceId(deviceId);

            if (deviceInfo != null) {
                // 设置来源为设备名称
                alarmInfo.setSource(deviceInfo.getDeviceName());

                // 设置部门相关信息
                alarmInfo.setDeptName(deviceInfo.getDeptName());
                alarmInfo.setDeptId(deviceInfo.getDeptId());

                // 设置组织相关信息
                alarmInfo.setOrgName(deviceInfo.getOrgName());
                alarmInfo.setOrgId(deviceInfo.getOrgId());
            } else {
                log.warn("⚠️ Device not found for device ID: {}", deviceId);
            }
            // 存储图片minio地址
            alarmInfo.setImage(imageUrl);

            // 设置报警等级为0
            alarmInfo.setLevel("0");

            // 设置处理状态 (0:未处理)
            alarmInfo.setStatus("0");

            // 手动设置创建和更新信息，避免依赖用户登录状态
            alarmInfo.setTenantId("000000");
            alarmInfo.setCreateBy(1L);
            alarmInfo.setCreateDept(deviceInfo.getDeptId());
            alarmInfo.setUpdateBy(1L);
            alarmInfo.setCreateTime(new Date());
            alarmInfo.setUpdateTime(new Date());
            // 保存到数据库
            alarmInfoMapper.insert(alarmInfo);

            log.info("✅ Alert event handled successfully for device: {}", deviceId);

        } catch (Exception e) {
            log.error("❌ Error handling alert event: {}", e.getMessage(), e);
        }
    }

    private void subscribeToTopics() {
        try {
            // 直接使用Mapper查询设备，绕过Service层的数据权限检查
            List<DeviceInfo> devices = deviceInfoMapper.selectList(
                    new LambdaQueryWrapper<DeviceInfo>()
                        .eq(DeviceInfo::getDeviceTags, 1L))
                .stream()
                .toList();

            if (devices.isEmpty()) {
                log.warn("⚠️ No devices found with tag 1, subscribing to default topic");
                // 如果没有找到设备，订阅默认主题
                client.subscribe("video/alert/34020000001320000005", 1);
                log.info("🔖 Subscribed to default topic: video/alert/34020000001320000005");
                return;
            }

            // 为每个设备订阅主题
            for (DeviceInfo device : devices) {
                String deviceId = device.getDeviceId();
                if (deviceId != null && !deviceId.isEmpty()) {
                    // 构造订阅主题: video/alert/{deviceId}
                    String topic = "video/alert/" + deviceId;
                    client.subscribe(topic, 0);
                    log.info("🔖 Subscribed to topic: {}", topic);
                }
            }

            log.info("📋 Successfully subscribed to {} device topics", devices.size());

        } catch (Exception e) {
            log.error("❌ Failed to subscribe to topics: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to subscribe to topics", e);
        }
    }

    @PreDestroy
    public void disconnect() {
        try {
            if (client != null && client.isConnected()) {
                client.disconnect();
                client.close();
                log.info("⏏️ Disconnected from MQTT broker");
            }
        } catch (MqttException e) {
            log.error("❌ Error disconnecting from MQTT broker: {}", e.getMessage(), e);
        }
    }
}
