package org.dromara.video.service;

import org.dromara.video.domain.vo.AlarmInfoVo;
import org.dromara.video.domain.bo.AlarmInfoBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 告警信息Service接口
 *
 * <AUTHOR>
 * @date 2025-09-05
 */
public interface IAlarmInfoService {

    /**
     * 批量修改告警信息
     *
     * @param bo 告警信息
     * @return 是否修改成功
     */
    Boolean updateBatchByIds(AlarmInfoBo bo);

    /**
     * 查询告警信息
     *
     * @param id 主键
     * @return 告警信息
     */
    AlarmInfoVo queryById(String id);

    /**
     * 分页查询告警信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 告警信息分页列表
     */
    TableDataInfo<AlarmInfoVo> queryPageList(AlarmInfoBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的告警信息列表
     *
     * @param bo 查询条件
     * @return 告警信息列表
     */
    List<AlarmInfoVo> queryList(AlarmInfoBo bo);

    /**
     * 新增告警信息
     *
     * @param bo 告警信息
     * @return 是否新增成功
     */
    Boolean insertByBo(AlarmInfoBo bo);

    /**
     * 修改告警信息
     *
     * @param bo 告警信息
     * @return 是否修改成功
     */
    Boolean updateByBo(AlarmInfoBo bo);

    /**
     * 校验并批量删除告警信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
