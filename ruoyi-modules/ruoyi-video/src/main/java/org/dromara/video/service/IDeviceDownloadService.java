package org.dromara.video.service;

import org.dromara.video.domain.vo.DeviceDownloadVo;
import org.dromara.video.domain.bo.DeviceDownloadBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 视频下载记录Service接口
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
public interface IDeviceDownloadService {

    /**
     * 查询视频下载记录
     *
     * @param id 主键
     * @return 视频下载记录
     */
    DeviceDownloadVo queryById(Long id);

    /**
     * 分页查询视频下载记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 视频下载记录分页列表
     */
    TableDataInfo<DeviceDownloadVo> queryPageList(DeviceDownloadBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的视频下载记录列表
     *
     * @param bo 查询条件
     * @return 视频下载记录列表
     */
    List<DeviceDownloadVo> queryList(DeviceDownloadBo bo);

    /**
     * 新增视频下载记录
     *
     * @param bo 视频下载记录
     * @return 是否新增成功
     */
    Boolean insertByBo(DeviceDownloadBo bo);

    /**
     * 修改视频下载记录
     *
     * @param bo 视频下载记录
     * @return 是否修改成功
     */
    Boolean updateByBo(DeviceDownloadBo bo);

    /**
     * 校验并批量删除视频下载记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
