package org.dromara.video.service;

import org.dromara.video.domain.vo.DeviceInfoVo;
import org.dromara.video.domain.bo.DeviceInfoBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 设备管理testService接口
 *
 * <AUTHOR>
 * @date 2025-08-22
 */
public interface IDeviceInfoService {
    /**
     * 启用设备
     *
     * @param deviceId 设备ID
     * @param token 访问令牌
     * @return 操作结果
     */
    Boolean deployDevice(String deviceId, String token);

    /**
     * 禁用设备
     *
     * @param deviceId 设备ID
     * @param token 访问令牌
     * @return 操作结果
     */
    Boolean undeployDevice(String deviceId, String token);

    /**
     * 更新设备在线状态
     *
     * @param bo 设备信息
     * @param token 访问令牌
     * @return 操作结果
     */
    Boolean updateOnlineStatus(DeviceInfoBo bo, String token);

    /**
     * 获取设备统计信息
     *
     * @return 统计信息Map
     */
    Map<String, Object> getDeviceStatistics();

    /**
     * 新增设备管理（包含外部API调用）
     *
     * @param bo 设备信息
     * @param token 访问令牌
     * @return 操作结果
     */
    Boolean addDeviceWithApi(DeviceInfoBo bo, String token);

    /**
     * 修改设备管理（包含外部API调用）
     *
     * @param bo 设备信息
     * @param token 访问令牌
     * @return 操作结果
     */
    Boolean editDeviceWithApi(DeviceInfoBo bo, String token);

    /**
     * 删除设备管理（包含外部API调用）
     *
     * @param ids 主键数组
     * @param token 访问令牌
     * @return 操作结果
     */
    Boolean removeDevicesWithApi(Long[] ids, String token);
    /**
     * 查询设备管理test
     *
     * @param id 主键
     * @return 设备管理test
     */
    DeviceInfoVo queryById(Long id);
    /**
     * 根据设备ID查询设备管理
     *
     * @param deviceId 设备ID
     * @return 设备管理test
     */
    DeviceInfoVo queryByDeviceId(String deviceId);
    /**
     * 分页查询设备管理test列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 设备管理test分页列表
     */

    TableDataInfo<DeviceInfoVo> queryPageList(DeviceInfoBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的设备管理test列表
     *
     * @param bo 查询条件
     * @return 设备管理test列表
     */

    List<DeviceInfoVo> queryList(DeviceInfoBo bo);

    /**
     * 新增设备管理test
     *
     * @param bo 设备管理test
     * @return 是否新增成功
     */
    Boolean insertByBo(DeviceInfoBo bo);

    /**
     * 修改设备管理test
     *
     * @param bo 设备管理test
     * @return 是否修改成功
     */
    Boolean updateByBo(DeviceInfoBo bo);

    /**
     * 校验并批量删除设备管理test信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);


}
