package org.dromara.video.service;

import org.dromara.video.domain.vo.DevicePlayEventsVo;
import org.dromara.video.domain.bo.DevicePlayEventsBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 设备播放事件明细Service接口
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
public interface IDevicePlayEventsService {

    /**
     * 查询设备播放事件明细
     *
     * @param id 主键
     * @return 设备播放事件明细
     */
    DevicePlayEventsVo queryById(Long id);

    /**
     * 分页查询设备播放事件明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 设备播放事件明细分页列表
     */
    TableDataInfo<DevicePlayEventsVo> queryPageList(DevicePlayEventsBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的设备播放事件明细列表
     *
     * @param bo 查询条件
     * @return 设备播放事件明细列表
     */
    List<DevicePlayEventsVo> queryList(DevicePlayEventsBo bo);

    /**
     * 新增设备播放事件明细
     *
     * @param bo 设备播放事件明细
     * @return 是否新增成功
     */
    Boolean insertByBo(DevicePlayEventsBo bo);

    /**
     * 修改设备播放事件明细
     *
     * @param bo 设备播放事件明细
     * @return 是否修改成功
     */
    Boolean updateByBo(DevicePlayEventsBo bo);

    /**
     * 校验并批量删除设备播放事件明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
