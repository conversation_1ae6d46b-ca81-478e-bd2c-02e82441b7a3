package org.dromara.video.service;

import org.dromara.video.domain.vo.DeviceRecordVo;
import org.dromara.video.domain.bo.DeviceRecordBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 云端视频文件回放Service接口
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
public interface IDeviceRecordService {

    /**
     * 查询云端视频文件回放
     *
     * @param id 主键
     * @return 云端视频文件回放
     */
    DeviceRecordVo queryById(Long id);

    /**
     * 分页查询云端视频文件回放列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 云端视频文件回放分页列表
     */
    TableDataInfo<DeviceRecordVo> queryPageList(DeviceRecordBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的云端视频文件回放列表
     *
     * @param bo 查询条件
     * @return 云端视频文件回放列表
     */
    List<DeviceRecordVo> queryList(DeviceRecordBo bo);

    /**
     * 新增云端视频文件回放
     *
     * @param bo 云端视频文件回放
     * @return 是否新增成功
     */
    Boolean insertByBo(DeviceRecordBo bo);

    /**
     * 修改云端视频文件回放
     *
     * @param bo 云端视频文件回放
     * @return 是否修改成功
     */
    Boolean updateByBo(DeviceRecordBo bo);

    /**
     * 校验并批量删除云端视频文件回放信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
