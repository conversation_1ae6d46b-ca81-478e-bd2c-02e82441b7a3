package org.dromara.video.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.video.domain.bo.AlarmInfoBo;
import org.dromara.video.domain.vo.AlarmInfoVo;
import org.dromara.video.domain.AlarmInfo;
import org.dromara.video.mapper.AlarmInfoMapper;
import org.dromara.video.service.IAlarmInfoService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 告警信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-05
 */
@RequiredArgsConstructor
@Service
public class AlarmInfoServiceImpl implements IAlarmInfoService {

    private final AlarmInfoMapper baseMapper;

    /**
     * 查询告警信息
     *
     * @param id 主键
     * @return 告警信息
     */
    @Override
    public AlarmInfoVo queryById(String id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询告警信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 告警信息分页列表
     */
    @DataPermission({
        @DataColumn(key = "deptName", value = "dept_id")
    })
    @Override
    public TableDataInfo<AlarmInfoVo> queryPageList(AlarmInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AlarmInfo> lqw = buildQueryWrapper(bo);
        Page<AlarmInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的告警信息列表
     *
     * @param bo 查询条件
     * @return 告警信息列表
     */
    @DataPermission({
        @DataColumn(key = "deptName", value = "dept_id")
    })
    @Override
    public List<AlarmInfoVo> queryList(AlarmInfoBo bo) {
        LambdaQueryWrapper<AlarmInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AlarmInfo> buildQueryWrapper(AlarmInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AlarmInfo> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(AlarmInfo::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceId()), AlarmInfo::getDeviceId, bo.getDeviceId());
        lqw.eq(StringUtils.isNotBlank(bo.getAlarmType()), AlarmInfo::getAlarmType, bo.getAlarmType());
        lqw.eq(bo.getAlarmTime() != null, AlarmInfo::getAlarmTime, bo.getAlarmTime());
        lqw.eq(StringUtils.isNotBlank(bo.getDescription()), AlarmInfo::getDescription, bo.getDescription());
        lqw.eq(StringUtils.isNotBlank(bo.getSource()), AlarmInfo::getSource, bo.getSource());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), AlarmInfo::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getImage()), AlarmInfo::getImage, bo.getImage());
        lqw.like(StringUtils.isNotBlank(bo.getDeptName()), AlarmInfo::getDeptName, bo.getDeptName());
        lqw.eq(bo.getDeptId() != null, AlarmInfo::getDeptId, bo.getDeptId());
        lqw.like(StringUtils.isNotBlank(bo.getOrgName()), AlarmInfo::getOrgName, bo.getOrgName());
        lqw.eq(bo.getOrgId() != null, AlarmInfo::getOrgId, bo.getOrgId());
        lqw.eq(StringUtils.isNotBlank(bo.getLevel()), AlarmInfo::getLevel, bo.getLevel());
        // 添加对processTime字段的查询条件
        lqw.eq(bo.getProcessTime() != null, AlarmInfo::getProcessTime, bo.getProcessTime());
        // 添加对processBy字段的查询条件
        lqw.eq(StringUtils.isNotBlank(bo.getProcessBy()), AlarmInfo::getProcessBy, bo.getProcessBy());
        // 添加根据开始和结束时间查询的功能
        lqw.between(params.get("beginTime") != null && params.get("endTime") != null,
            AlarmInfo::getAlarmTime, params.get("beginTime"), params.get("endTime"));
//        if (params.get("beginTime") != null && params.get("endTime") != null) {
//            Object beginTime = params.get("beginTime");
//            Object endTime = params.get("endTime");
//            // 如果参数是字符串类型，则尝试转换为日期类型
//            if (beginTime instanceof String && endTime instanceof String) {
//                try {
//                    // 尝试解析常见的日期格式
//                    String beginStr = beginTime.toString();
//                    String endStr = endTime.toString();
//
//                    // 尝试解析 yyyy-MM-dd HH:mm:ss 格式
//                    java.time.format.DateTimeFormatter formatter = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//                    java.time.LocalDateTime beginDateTime = java.time.LocalDateTime.parse(beginStr, formatter);
//                    java.time.LocalDateTime endDateTime = java.time.LocalDateTime.parse(endStr, formatter);
//
//                    lqw.between(AlarmInfo::getAlarmTime, beginDateTime, endDateTime);
//                } catch (Exception e) {
//                    try {
//                        // 尝试解析 yyyy-MM-dd 格式
//                        java.time.format.DateTimeFormatter formatter = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd");
//                        java.time.LocalDate beginDate = java.time.LocalDate.parse(beginTime.toString(), formatter);
//                        java.time.LocalDate endDate = java.time.LocalDate.parse(endTime.toString(), formatter);
//
//                        lqw.between(AlarmInfo::getAlarmTime, beginDate.atStartOfDay(), endDate.atStartOfDay().plusDays(1).minusSeconds(1));
//                    } catch (Exception ex) {
//                        // 如果解析失败，则直接使用原始值（可能会导致错误）
//                        lqw.between(AlarmInfo::getAlarmTime, beginTime, endTime);
//                    }
//                }
//            } else {
//                lqw.between(AlarmInfo::getAlarmTime, beginTime, endTime);
//            }
//        }
        return lqw;
    }

    /**
     * 新增告警信息
     *
     * @param bo 告警信息
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(AlarmInfoBo bo) {
        AlarmInfo add = MapstructUtils.convert(bo, AlarmInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改告警信息
     *
     * @param bo 告警信息
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(AlarmInfoBo bo) {
        AlarmInfo update = MapstructUtils.convert(bo, AlarmInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }
    /**
     * 批量修改告警信息
     *
     * @param bo 告警信息
     * @return 是否修改成功
     */
    @Override
    public Boolean updateBatchByIds(AlarmInfoBo bo) {
        if (bo.getIds() == null || bo.getIds().isEmpty()) {
            return false;
        }

        LambdaUpdateWrapper<AlarmInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(AlarmInfo::getId, bo.getIds());
        // 使用传递的status值
        if (StringUtils.isNotBlank(bo.getStatus())) {
            updateWrapper.set(AlarmInfo::getStatus, bo.getStatus());
        } else {
            updateWrapper.set(AlarmInfo::getStatus, "0"); // 默认设置为未处理
        }
        // 设置处理时间和处理人
        // 设置处理时间和处理人（由后端自动获取）
        updateWrapper.set(AlarmInfo::getProcessTime, new java.util.Date());
        updateWrapper.set(AlarmInfo::getProcessBy, org.dromara.common.satoken.utils.LoginHelper.getUsername());
        // 如果有处理意见，则设置处理意见
        if (StringUtils.isNotBlank(bo.getDescription())) {
            updateWrapper.set(AlarmInfo::getDescription, bo.getDescription());
        }
        // 如果有备注，则设置备注
        if (StringUtils.isNotBlank(bo.getRemark())) {
            updateWrapper.set(AlarmInfo::getRemark, bo.getRemark());
        }
        return baseMapper.update(null, updateWrapper) > 0;
    }
    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AlarmInfo entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除告警信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
