package org.dromara.video.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.video.domain.bo.DeviceDownloadBo;
import org.dromara.video.domain.vo.DeviceDownloadVo;
import org.dromara.video.domain.DeviceDownload;
import org.dromara.video.mapper.DeviceDownloadMapper;
import org.dromara.video.service.IDeviceDownloadService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 视频下载记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@RequiredArgsConstructor
@Service
public class DeviceDownloadServiceImpl implements IDeviceDownloadService {

    private final DeviceDownloadMapper baseMapper;

    /**
     * 查询视频下载记录
     *
     * @param id 主键
     * @return 视频下载记录
     */
    @Override
    public DeviceDownloadVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询视频下载记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 视频下载记录分页列表
     */
    @Override
    public TableDataInfo<DeviceDownloadVo> queryPageList(DeviceDownloadBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<DeviceDownload> lqw = buildQueryWrapper(bo);
        Page<DeviceDownloadVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的视频下载记录列表
     *
     * @param bo 查询条件
     * @return 视频下载记录列表
     */
    @Override
    public List<DeviceDownloadVo> queryList(DeviceDownloadBo bo) {
        LambdaQueryWrapper<DeviceDownload> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<DeviceDownload> buildQueryWrapper(DeviceDownloadBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DeviceDownload> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(DeviceDownload::getId);
        lqw.eq(bo.getFileId() != null, DeviceDownload::getFileId, bo.getFileId());
        lqw.eq(bo.getUserId() != null, DeviceDownload::getUserId, bo.getUserId());
        lqw.like(StringUtils.isNotBlank(bo.getUserName()), DeviceDownload::getUserName, bo.getUserName());
        lqw.like(StringUtils.isNotBlank(bo.getFileName()), DeviceDownload::getFileName, bo.getFileName());
        lqw.eq(StringUtils.isNotBlank(bo.getFileUrl()), DeviceDownload::getFileUrl, bo.getFileUrl());
        lqw.eq(bo.getDownloadTimes() != null, DeviceDownload::getDownloadTimes, bo.getDownloadTimes());
        lqw.eq(bo.getFileState() != null, DeviceDownload::getFileState, bo.getFileState());
        return lqw;
    }

    /**
     * 新增视频下载记录
     *
     * @param bo 视频下载记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(DeviceDownloadBo bo) {
        DeviceDownload add = MapstructUtils.convert(bo, DeviceDownload.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改视频下载记录
     *
     * @param bo 视频下载记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(DeviceDownloadBo bo) {
        DeviceDownload update = MapstructUtils.convert(bo, DeviceDownload.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(DeviceDownload entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除视频下载记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
