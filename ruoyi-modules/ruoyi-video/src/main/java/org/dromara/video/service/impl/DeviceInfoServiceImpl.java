package org.dromara.video.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.video.service.VideoPlatformService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.dromara.video.domain.bo.DeviceInfoBo;
import org.dromara.video.domain.vo.DeviceInfoVo;
import org.dromara.video.domain.DeviceInfo;
import org.dromara.video.mapper.DeviceInfoMapper;
import org.dromara.video.service.IDeviceInfoService;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.*;

/**
 * 设备管理testService业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-22
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class DeviceInfoServiceImpl implements IDeviceInfoService {

    private final DeviceInfoMapper baseMapper;
    private final VideoPlatformService videoPlatformService;

    @Value("${video.base-url}")
    private String videoBaseUrl;

    /**
     * 启用设备
     *
     * @param deviceId 设备ID
     * @param token 访问令牌
     * @return 操作结果
     */
    @Override
    public Boolean deployDevice(String deviceId, String token) {
        // 先刷新平台token
        String refreshedToken = videoPlatformService.refreshToken();
        // 如果传入的token为空，则使用刷新后的token
        String effectiveToken = (token != null && !token.isEmpty()) ? token : refreshedToken;

        try {
            // 创建HttpClient实例
            HttpClient client = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(10))
                .build();

            // 构建请求URL
            String url = videoBaseUrl + "/api/device-instance/" + deviceId + "/deploy";

            // 创建POST请求
            HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .header("x-access-token", effectiveToken)
                .header("Content-Type", "application/json")
                .POST(HttpRequest.BodyPublishers.noBody())
                .build();

            // 发送请求并获取响应
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());

            // 检查响应状态码
            if (response.statusCode() == 200) {
                // 更新数据库中的在线状态为online
                DeviceInfoBo updateBo = new DeviceInfoBo();
                updateBo.setDeviceId(deviceId);
                updateBo.setOnlineStatus("online");
                updateByBo(updateBo);
                return true;
            } else {
                log.error("启用设备失败，外部API调用失败，状态码: " + response.statusCode());
                return false;
            }
        } catch (Exception e) {
            log.error("启用设备过程中发生错误: " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * 禁用设备
     *
     * @param deviceId 设备ID
     * @param token 访问令牌
     * @return 操作结果
     */
    @Override
    public Boolean undeployDevice(String deviceId, String token) {
        // 先刷新平台token
        String refreshedToken = videoPlatformService.refreshToken();
        // 如果传入的token为空，则使用刷新后的token
        String effectiveToken = (token != null && !token.isEmpty()) ? token : refreshedToken;

        try {
            // 创建HttpClient实例
            HttpClient client = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(10))
                .build();

            // 构建请求URL
            String url = videoBaseUrl + "/api/device-instance/" + deviceId + "/undeploy";

            // 创建POST请求
            HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .header("x-access-token", effectiveToken)
                .header("Content-Type", "application/json")
                .POST(HttpRequest.BodyPublishers.noBody())
                .build();

            // 发送请求并获取响应
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());

            // 检查响应状态码
            if (response.statusCode() == 200) {
                // 更新数据库中的在线状态为notActive
                DeviceInfoBo updateBo = new DeviceInfoBo();
                updateBo.setDeviceId(deviceId);
                updateBo.setOnlineStatus("notActive");
                updateByBo(updateBo);
                return true;
            } else {
                log.error("禁用设备失败，外部API调用失败，状态码: " + response.statusCode());
                return false;
            }
        } catch (Exception e) {
            log.error("禁用设备过程中发生错误: " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * 更新设备在线状态
     *
     * @param bo 设备信息
     * @param token 访问令牌
     * @return 操作结果
     */
    @Override
    public Boolean updateOnlineStatus(DeviceInfoBo bo, String token) {
        // 更新设备在线状态
        List<DeviceInfoVo> allDevices = queryList(bo);
        // 先刷新平台token
        String refreshedToken = videoPlatformService.refreshToken();
        // 如果传入的token为空，则使用刷新后的token
        String effectiveToken = (token != null && !token.isEmpty()) ? token : refreshedToken;

        // 向外部API发送GET请求获取最新设备在线状态
        try {
            HttpClient client = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(10))
                .build();

            HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(videoBaseUrl + "/api/media/device/_query/no-paging"))
                .header("x-access-token", effectiveToken)
                .header("Content-Type", "application/json")
                .GET()
                .build();

            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            log.info("External API response status: " + response.statusCode());

            // 解析响应并更新设备信息
            if (response.statusCode() == 200 && response.body() != null) {
                ObjectMapper objectMapper = new ObjectMapper();
                JsonNode rootNode = objectMapper.readTree(response.body());

                // 检查响应是否成功并包含结果
                if (rootNode.has("result") && rootNode.get("result").isArray()) {
                    JsonNode resultArray = rootNode.get("result");

                    // 创建API设备ID到设备信息的映射，便于快速查找
                    Map<String, JsonNode> apiDeviceMap = new HashMap<>();
                    for (JsonNode deviceNode : resultArray) {
                        if (deviceNode.has("id")) {
                            apiDeviceMap.put(deviceNode.get("id").asText(), deviceNode);
                        }
                    }

                    // 遍历本地设备，更新状态
                    for (DeviceInfoVo localDevice : allDevices) {
                        String deviceId = localDevice.getDeviceId();

                        // 检查本地设备是否在API返回的设备列表中
                        if (apiDeviceMap.containsKey(deviceId)) {
                            JsonNode apiDevice = apiDeviceMap.get(deviceId);
                            // 只提取状态值，而不是整个JSON对象
                            if (apiDevice.has("state") && apiDevice.get("state").has("value")) {
                                String apiStatus = apiDevice.get("state").get("value").asText().trim();
                                // 只有当API状态与本地状态不一致时才更新
                                if (!apiStatus.equals(localDevice.getOnlineStatus())) {
                                    DeviceInfoBo updateBo = new DeviceInfoBo();
                                    updateBo.setId(localDevice.getId());
                                    updateBo.setOnlineStatus(apiStatus); // 正确设置状态值: online, offline
                                    // 执行更新操作
                                    updateByBo(updateBo);
                                }
                            }
                        }
                    }
                }
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("Failed to send request to external API: " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取设备统计信息
     *
     * @return 统计信息Map
     */
    @Override
    public Map<String, Object> getDeviceStatistics() {
        // 查询统计信息
        DeviceInfoBo queryBo = new DeviceInfoBo();
        List<DeviceInfoVo> devices = queryList(queryBo);
        long total = devices.size();
        long online = devices.stream()
            .filter(device -> "online".equals(device.getOnlineStatus()))
            .count();
        long offline = devices.stream()
            .filter(device -> "offline".equals(device.getOnlineStatus()))
            .count();
        long notActive = devices.stream()
            .filter(device -> "notActive".equals(device.getOnlineStatus()))
            .count();

        Map<String, Object> result = new HashMap<>();
        result.put("total", total);
        result.put("online", online);
        result.put("offline", offline);
        result.put("notActive", notActive);

        return result;
    }

    /**
     * 新增设备管理（包含外部API调用）
     *
     * @param bo 设备信息
     * @param token 访问令牌
     * @return 操作结果
     */
    @Override
    public Boolean addDeviceWithApi(DeviceInfoBo bo, String token) {
        // 先刷新平台token
        String refreshedToken = videoPlatformService.refreshToken();
        // 如果传入的token为空，则使用刷新后的token
        String effectiveToken = (token != null && !token.isEmpty()) ? token : refreshedToken;

        // 构造外部API请求数据
        Map<String, Object> externalDevice = new HashMap<>();
        externalDevice.put("id", bo.getDeviceId());  // 设备ID 必填
        externalDevice.put("name", bo.getDeviceName()); // 设备名称 必填
        externalDevice.put("streamMode", bo.getDeviceTrans()); // 传输模式 选填
        externalDevice.put("productId", bo.getDeviceType());  // 产品ID 必填
        externalDevice.put("channel", bo.getAccessProtocol());  // 接入方式 必填
        // 构造 others 子对象
        Map<String, String> others = new HashMap<>();
        others.put("access_pwd", bo.getAccessPassword());
        externalDevice.put("others", others);

        // 构造 photoUrl
        externalDevice.put("photoUrl", videoBaseUrl + "/assets/device-media.png");

        // 发送外部API请求
        try {
            HttpClient client = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(10))
                .build();

            HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(videoBaseUrl + "/api/media/device/gb28181-2016"))  // 假设创建设备的API路径
                .header("x-access-token", effectiveToken)
                .header("Content-Type", "application/json")
                .POST(HttpRequest.BodyPublishers.ofString(new ObjectMapper().writeValueAsString(externalDevice)))
                .build();

            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            log.info("External API create response status: " + response.statusCode());

            // 如果外部API调用失败，返回false
            if (response.statusCode() != 200 && response.statusCode() != 201) {
                log.error("Failed to create device in external system: " + response.body());
                return false;
            }
        } catch (Exception e) {
            log.error("Failed to send create request to external API: " + e.getMessage(), e);
            return false;
        }

        // 本地数据库持久化
        return insertByBo(bo);
    }

    /**
     * 修改设备管理（包含外部API调用）
     *
     * @param bo 设备信息
     * @param token 访问令牌
     * @return 操作结果
     */
    @Override
    public Boolean editDeviceWithApi(DeviceInfoBo bo, String token) {
        // 先刷新平台token
        String refreshedToken = videoPlatformService.refreshToken();
        // 如果传入的token为空，则使用刷新后的token
        String effectiveToken = (token != null && !token.isEmpty()) ? token : refreshedToken;

        // 构造外部API请求数据
        Map<String, Object> externalDevice = new HashMap<>();
        externalDevice.put("id", bo.getDeviceId());
        externalDevice.put("name", bo.getDeviceName());
        externalDevice.put("streamMode", bo.getDeviceTrans());
        externalDevice.put("productId", bo.getDeviceType());  // 产品ID 必填
        externalDevice.put("channel", bo.getAccessProtocol());  // 接入方式
        externalDevice.put("transport", bo.getDeviceTrans());
        externalDevice.put("provider", "gb28181-2016");
        externalDevice.put("password", bo.getAccessPassword());

        // 构造 others 子对象
        Map<String, String> others = new HashMap<>();
        others.put("access_pwd", bo.getAccessPassword());
        others.put("stream_mode", bo.getDeviceTrans());
        externalDevice.put("others", others);

        // 构造 photoUrl
        externalDevice.put("photoUrl", videoBaseUrl + "/assets/device-media.png");

        // 发送外部API请求
        try {
            HttpClient client = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(10))
                .build();

            String apiUrl = String.format(videoBaseUrl + "/api/media/device/gb28181-2016/%s", bo.getDeviceId());

            HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(apiUrl))
                .header("x-access-token", effectiveToken)
                .header("Content-Type", "application/json")
                .PUT(HttpRequest.BodyPublishers.ofString(new ObjectMapper().writeValueAsString(externalDevice)))
                .build();

            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            log.info("External API update response status: " + response.statusCode());
            log.debug("External API update response body: " + response.body());

            // 如果外部API调用失败，返回false
            if (response.statusCode() != 200) {
                log.error("Failed to update device in external system. Status: " + response.statusCode() + ", Body: " + response.body());
                return false;
            }
        } catch (Exception e) {
            log.error("Failed to send update request to external API: " + e.getMessage(), e);
            return false;
        }

        // 本地数据库持久化
        return updateByBo(bo);
    }

    /**
     * 删除设备管理（包含外部API调用）
     *
     * @param ids 主键数组
     * @param token 访问令牌
     * @return 操作结果
     */
    @Override
    public Boolean removeDevicesWithApi(Long[] ids, String token) {
        // 先刷新平台token
        String refreshedToken = videoPlatformService.refreshToken();
        // 如果传入的token为空，则使用刷新后的token
        String effectiveToken = (token != null && !token.isEmpty()) ? token : refreshedToken;

        // 逐个删除设备（外部系统删除优先）
        for (Long id : ids) {
            try {
                // 查询本地设备信息获取设备ID
                DeviceInfoVo deviceInfo = queryById(id);
                if (deviceInfo == null) {
                    log.error("设备ID: " + id + " 在本地数据库中不存在");
                    return false;
                }

                String deviceId = deviceInfo.getDeviceId();
                if (deviceId == null || deviceId.isEmpty()) {
                    log.error("设备ID: " + id + " 的设备编码为空");
                    return false;
                }

                // 构造外部API请求地址
                String apiUrl = String.format(videoBaseUrl + "/api/media/device/%s", deviceId);

                // 创建HTTP客户端
                HttpClient client = HttpClient.newBuilder()
                    .connectTimeout(Duration.ofSeconds(10))
                    .build();

                // 构建DELETE请求
                HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(apiUrl))
                    .header("x-access-token", effectiveToken)
                    .header("Content-Type", "application/json")
                    .DELETE()
                    .build();

                // 发送请求
                HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());

                // 记录响应信息
                log.info("External API delete response status for ID " + id + ": " + response.statusCode());
                log.debug("External API delete response body for ID " + id + ": " + response.body());

                // 校验响应状态
                if (response.statusCode() != 200) {
                    log.error("Failed to delete device in external system. ID: " + id +
                        ", Status: " + response.statusCode() +
                        ", Body: " + response.body());
                    return false;
                }
            } catch (Exception e) {
                log.error("Failed to send delete request to external API for ID " + id + ": " + e.getMessage(), e);
                return false;
            }
        }

        // 本地数据库批量删除
        return deleteWithValidByIds(List.of(ids), true);
    }




    /**
     * 查询设备管理test
     *
     * @param id 主键
     * @return 设备管理test
     */
    @Override
    public DeviceInfoVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }
    /**
     * 根据设备ID查询设备管理
     *
     * @param deviceId 设备ID
     * @return 设备管理test
     */
    @Override
    public DeviceInfoVo queryByDeviceId(String deviceId){
        LambdaQueryWrapper<DeviceInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(DeviceInfo::getDeviceId, deviceId);
        return baseMapper.selectVoOne(lqw);
    }
    /**
     * 分页查询设备管理test列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 设备管理test分页列表
     */
    @DataPermission({
        @DataColumn(key = "deptName", value = "dept_id")
    })
    @Override
    public TableDataInfo<DeviceInfoVo> queryPageList(DeviceInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<DeviceInfo> lqw = buildQueryWrapper(bo);
        Page<DeviceInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的设备管理test列表
     *
     * @param bo 查询条件
     * @return 设备管理test列表
     */
    @DataPermission({
        @DataColumn(key = "deptName", value = "dept_id")
    })
    @Override
    public List<DeviceInfoVo> queryList(DeviceInfoBo bo) {
        LambdaQueryWrapper<DeviceInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<DeviceInfo> buildQueryWrapper(DeviceInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DeviceInfo> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(DeviceInfo::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getGbId()), DeviceInfo::getGbId, bo.getGbId());
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceId()), DeviceInfo::getDeviceId, bo.getDeviceId());
        lqw.like(StringUtils.isNotBlank(bo.getDeviceName()), DeviceInfo::getDeviceName, bo.getDeviceName());
        lqw.like(StringUtils.isNotBlank(bo.getDeviceUsername()), DeviceInfo::getDeviceUsername, bo.getDeviceUsername());
        lqw.eq(StringUtils.isNotBlank(bo.getDevicePassword()), DeviceInfo::getDevicePassword, bo.getDevicePassword());
        lqw.eq(bo.getDeviceType() != null, DeviceInfo::getDeviceType, bo.getDeviceType());
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceTrans()), DeviceInfo::getDeviceTrans, bo.getDeviceTrans());
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceVendor()), DeviceInfo::getDeviceVendor, bo.getDeviceVendor());
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceModel()), DeviceInfo::getDeviceModel, bo.getDeviceModel());
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceIp()), DeviceInfo::getDeviceIp, bo.getDeviceIp());
        lqw.eq(bo.getDevicePort() != null, DeviceInfo::getDevicePort, bo.getDevicePort());
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceVersion()), DeviceInfo::getDeviceVersion, bo.getDeviceVersion());
        lqw.eq(bo.getDeviceTags() != null, DeviceInfo::getDeviceTags, bo.getDeviceTags());
        lqw.eq(StringUtils.isNotBlank(bo.getAccessAccount()), DeviceInfo::getAccessAccount, bo.getAccessAccount());
        lqw.eq(StringUtils.isNotBlank(bo.getAccessPassword()), DeviceInfo::getAccessPassword, bo.getAccessPassword());
        lqw.eq(StringUtils.isNotBlank(bo.getAccessProtocol()), DeviceInfo::getAccessProtocol, bo.getAccessProtocol());
        lqw.eq(StringUtils.isNotBlank(bo.getSipAddress()), DeviceInfo::getSipAddress, bo.getSipAddress());
        lqw.eq(bo.getRegisterTime() != null, DeviceInfo::getRegisterTime, bo.getRegisterTime());
        lqw.eq(bo.getExpireTime() != null, DeviceInfo::getExpireTime, bo.getExpireTime());
        lqw.eq(StringUtils.isNotBlank(bo.getOnlineStatus()), DeviceInfo::getOnlineStatus, bo.getOnlineStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getEnableStatus()), DeviceInfo::getEnableStatus, bo.getEnableStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getDeptName()), DeviceInfo::getDeptName, bo.getDeptName());
        lqw.eq(StringUtils.isNotBlank(bo.getOrgName()), DeviceInfo::getOrgName, bo.getOrgName());
        lqw.eq(bo.getDeptId() !=null, DeviceInfo::getDeptId, bo.getDeptId());
        lqw.eq(StringUtils.isNotBlank(bo.getLocation()), DeviceInfo::getLocation, bo.getLocation());
        lqw.eq(bo.getLongitude() != null, DeviceInfo::getLongitude, bo.getLongitude());
        lqw.eq(bo.getLatitude() != null, DeviceInfo::getLatitude, bo.getLatitude());
        lqw.eq(bo.getOrgId() != null, DeviceInfo::getOrgId, bo.getOrgId());
        lqw.eq(StringUtils.isNotBlank(bo.getChannelId()), DeviceInfo::getChannelId, bo.getChannelId());
        lqw.eq(StringUtils.isNotBlank(bo.getIsOrg()), DeviceInfo::getIsOrg, bo.getIsOrg());
        lqw.eq(StringUtils.isNotBlank(bo.getIsBound()), DeviceInfo::getIsBound, bo.getIsBound());
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceImei()), DeviceInfo::getDeviceImei, bo.getDeviceImei());
        // 支持通过多个部门名称查询数据
        if (StringUtils.isNotBlank(bo.getDeptNames())) {
            // 将逗号分隔的部门名称拆分为列表
            List<String> deptNames = Arrays.asList(bo.getDeptNames().split(","));
            lqw.in(DeviceInfo::getDeptName, deptNames);
        } else if (StringUtils.isNotBlank(bo.getDeptName())) {
            lqw.like(DeviceInfo::getDeptName, bo.getDeptName());
        }

        // 支持通过多个组织名称查询数据
        if (StringUtils.isNotBlank(bo.getOrgNames())) {
            // 将逗号分隔的组织名称拆分为列表
            List<String> orgNames = Arrays.asList(bo.getOrgNames().split(","));
            lqw.in(DeviceInfo::getOrgName, orgNames);
        } else if (StringUtils.isNotBlank(bo.getOrgName())) {
            lqw.like(DeviceInfo::getOrgName, bo.getOrgName());
        }
        return lqw;
    }

    /**
     * 新增设备管理test
     *
     * @param bo 设备管理test
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(DeviceInfoBo bo) {
        DeviceInfo add = MapstructUtils.convert(bo, DeviceInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改设备管理test
     *
     * @param bo 设备管理test
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(DeviceInfoBo bo) {
        DeviceInfo update = MapstructUtils.convert(bo, DeviceInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(DeviceInfo entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除设备管理test信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }




}
