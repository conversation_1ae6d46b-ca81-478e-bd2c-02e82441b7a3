package org.dromara.video.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.video.domain.bo.DevicePlayEventsBo;
import org.dromara.video.domain.vo.DevicePlayEventsVo;
import org.dromara.video.domain.DevicePlayEvents;
import org.dromara.video.mapper.DevicePlayEventsMapper;
import org.dromara.video.service.IDevicePlayEventsService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 设备播放事件明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@RequiredArgsConstructor
@Service
public class DevicePlayEventsServiceImpl implements IDevicePlayEventsService {

    private final DevicePlayEventsMapper baseMapper;

    /**
     * 查询设备播放事件明细
     *
     * @param id 主键
     * @return 设备播放事件明细
     */
    @Override
    public DevicePlayEventsVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询设备播放事件明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 设备播放事件明细分页列表
     */
    @Override
    public TableDataInfo<DevicePlayEventsVo> queryPageList(DevicePlayEventsBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<DevicePlayEvents> lqw = buildQueryWrapper(bo);
        Page<DevicePlayEventsVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的设备播放事件明细列表
     *
     * @param bo 查询条件
     * @return 设备播放事件明细列表
     */
    @Override
    public List<DevicePlayEventsVo> queryList(DevicePlayEventsBo bo) {
        LambdaQueryWrapper<DevicePlayEvents> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<DevicePlayEvents> buildQueryWrapper(DevicePlayEventsBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DevicePlayEvents> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(DevicePlayEvents::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceId()), DevicePlayEvents::getDeviceId, bo.getDeviceId());
        lqw.eq(bo.getUserId() != null, DevicePlayEvents::getUserId, bo.getUserId());
        lqw.like(StringUtils.isNotBlank(bo.getUserName()), DevicePlayEvents::getUserName, bo.getUserName());
        lqw.eq(bo.getStartTime() != null, DevicePlayEvents::getStartTime, bo.getStartTime());
        lqw.eq(bo.getEndTime() != null, DevicePlayEvents::getEndTime, bo.getEndTime());
        lqw.eq(bo.getDuration() != null, DevicePlayEvents::getDuration, bo.getDuration());
        lqw.eq(bo.getEventDate() != null, DevicePlayEvents::getEventDate, bo.getEventDate());
        return lqw;
    }

    /**
     * 新增设备播放事件明细
     *
     * @param bo 设备播放事件明细
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(DevicePlayEventsBo bo) {
        DevicePlayEvents add = MapstructUtils.convert(bo, DevicePlayEvents.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改设备播放事件明细
     *
     * @param bo 设备播放事件明细
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(DevicePlayEventsBo bo) {
        DevicePlayEvents update = MapstructUtils.convert(bo, DevicePlayEvents.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(DevicePlayEvents entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除设备播放事件明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
