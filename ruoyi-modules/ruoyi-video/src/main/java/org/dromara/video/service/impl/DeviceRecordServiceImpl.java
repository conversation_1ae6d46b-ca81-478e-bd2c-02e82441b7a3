package org.dromara.video.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.video.domain.bo.DeviceRecordBo;
import org.dromara.video.domain.vo.DeviceRecordVo;
import org.dromara.video.domain.DeviceRecord;
import org.dromara.video.mapper.DeviceRecordMapper;
import org.dromara.video.service.IDeviceRecordService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 云端视频文件回放Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@RequiredArgsConstructor
@Service
public class DeviceRecordServiceImpl implements IDeviceRecordService {

    private final DeviceRecordMapper baseMapper;

    /**
     * 查询云端视频文件回放
     *
     * @param id 主键
     * @return 云端视频文件回放
     */
    @Override
    public DeviceRecordVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询云端视频文件回放列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 云端视频文件回放分页列表
     */
    @Override
    public TableDataInfo<DeviceRecordVo> queryPageList(DeviceRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<DeviceRecord> lqw = buildQueryWrapper(bo);
        Page<DeviceRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的云端视频文件回放列表
     *
     * @param bo 查询条件
     * @return 云端视频文件回放列表
     */
    @Override
    public List<DeviceRecordVo> queryList(DeviceRecordBo bo) {
        LambdaQueryWrapper<DeviceRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<DeviceRecord> buildQueryWrapper(DeviceRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DeviceRecord> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(DeviceRecord::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceId()), DeviceRecord::getDeviceId, bo.getDeviceId());
        lqw.eq(bo.getTakeTime() != null, DeviceRecord::getTakeTime, bo.getTakeTime());
        lqw.eq(bo.getRecordTime() != null, DeviceRecord::getRecordTime, bo.getRecordTime());
        lqw.eq(StringUtils.isNotBlank(bo.getFileUrl()), DeviceRecord::getFileUrl, bo.getFileUrl());
        lqw.like(StringUtils.isNotBlank(bo.getDeviceName()), DeviceRecord::getDeviceName, bo.getDeviceName());
        lqw.like(StringUtils.isNotBlank(bo.getDeptName()), DeviceRecord::getDeptName, bo.getDeptName());
        lqw.eq(StringUtils.isNotBlank(bo.getDeptId()), DeviceRecord::getDeptId, bo.getDeptId());
        lqw.eq(StringUtils.isNotBlank(bo.getFileType()), DeviceRecord::getFileType, bo.getFileType());
        lqw.eq(StringUtils.isNotBlank(bo.getFileTags()), DeviceRecord::getFileTags, bo.getFileTags());
        lqw.eq(bo.getFileSize() != null, DeviceRecord::getFileSize, bo.getFileSize());
        lqw.eq(bo.getFileTime() != null, DeviceRecord::getFileTime, bo.getFileTime());
        lqw.eq(bo.getPanId() != null, DeviceRecord::getPanId, bo.getPanId());
        lqw.eq(bo.getPlayCount() != null, DeviceRecord::getPlayCount, bo.getPlayCount());
        lqw.eq(bo.getVideoRecordId() != null, DeviceRecord::getVideoRecordId, bo.getVideoRecordId());
        return lqw;
    }

    /**
     * 新增云端视频文件回放
     *
     * @param bo 云端视频文件回放
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(DeviceRecordBo bo) {
        DeviceRecord add = MapstructUtils.convert(bo, DeviceRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改云端视频文件回放
     *
     * @param bo 云端视频文件回放
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(DeviceRecordBo bo) {
        DeviceRecord update = MapstructUtils.convert(bo, DeviceRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(DeviceRecord entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除云端视频文件回放信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
