package org.dromara.video.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.video.domain.bo.PlatformTokenBo;
import org.dromara.video.service.VideoPlatformService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Map;

@Service
@Slf4j
public class VideoPlatformServiceImpl implements VideoPlatformService {
    @Value("${video.base-url}")
    private String videoBaseUrl;
    @Value("${video.client-id}")
    private String videoClientId;
    @Value("${video.client-secret}")
    private String videoSecret;
    @Value("${video.grant-type}")
    private String videoGrantType;

    private PlatformTokenBo platformTokenBo;
    @Override
    public String refreshToken() {
        if(platformTokenBo == null){
            log.info("没有中台token，进行获取");
            platformTokenBo = getToken();
        }else{
            long timestamp = platformTokenBo.getTimestamp();
            if(System.currentTimeMillis()-timestamp > 3600 * 1000){
                log.info("中台token过期，重新获取");
                platformTokenBo = getToken();
            }
        }
        return platformTokenBo.getAccessToken();
    }

    private PlatformTokenBo getToken() {
        HttpClient client = HttpClient.newBuilder()
            .connectTimeout(Duration.ofSeconds(10))
            .build();
        String url = videoBaseUrl + "/api/oauth2/token";
        StringBuilder sb = new StringBuilder();
        sb.append("grant_type=").append(videoGrantType)
            .append("&client_id=").append(videoClientId)
            .append("&client_secret=").append(videoSecret);
        try{
            // 创建POST请求
            HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .header("Content-Type", "application/x-www-form-urlencoded")
                .POST(HttpRequest.BodyPublishers.ofString(sb.toString(), StandardCharsets.UTF_8))
                .build();
            // 4. 发送请求并获取响应
            HttpResponse<String> response = client.send(
                request, HttpResponse.BodyHandlers.ofString());

            String body = response.body();
            Map<String, Object> map = JsonUtils.parseObject(body, Map.class);
            if(response.statusCode()!=200){
                throw new RuntimeException(MapUtils.getInteger(map, "status") + " "+MapUtils.getString(map, "error")+" "+url);
            }

            platformTokenBo = new PlatformTokenBo().setAccessToken(MapUtils.getString(map, "access_token"))
                                .setRefreshToken(MapUtils.getString(map, "refresh_token"));
            platformTokenBo.setTimestamp(System.currentTimeMillis());
            return platformTokenBo;
        }catch (Exception e){
            log.error(e.getMessage(), e);
            throw new RuntimeException("获取视频中台token异常"+e.getMessage());
        }
    }
}
