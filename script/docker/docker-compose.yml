services:

  nginx-web:
    image: nginx:1.23.4
    container_name: nginx-web
    environment:
      # 时区上海
      TZ: Asia/Shanghai
    ports:
      - "80:80"
      - "443:443"
    volumes:
      # 证书映射
      - /docker/nginx/cert:/etc/nginx/cert
      # 配置文件映射
      - /docker/nginx/conf/nginx.conf:/etc/nginx/nginx.conf
      # 页面目录
      - /docker/nginx/html:/usr/share/nginx/html
      # 日志目录
      - /docker/nginx/log:/var/log/nginx
    privileged: true
    network_mode: "host"


  ruoyi-server1:
    image: ruoyi/ruoyi-server:5.3.1
    container_name: ruoyi-server1
    environment:
      # 时区上海
      TZ: Asia/Shanghai
      SERVER_PORT: 8080
    volumes:
      # 配置文件
      - /docker/server1/logs/:/ruoyi/server/logs/
      # skywalking 探针
#      - /docker/skywalking/agent/:/ruoyi/skywalking/agent
    privileged: true
    network_mode: "host"

  ruoyi-server2:
    image: ruoyi/ruoyi-server:5.3.1
    container_name: ruoyi-server2
    environment:
      # 时区上海
      TZ: Asia/Shanghai
      SERVER_PORT: 8081
    volumes:
      # 配置文件
      - /docker/server2/logs/:/ruoyi/server/logs/
      # skywalking 探针
#      - /docker/skywalking/agent/:/ruoyi/skywalking/agent
    privileged: true
    network_mode: "host"

