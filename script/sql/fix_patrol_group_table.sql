-- 修复出巡分组表字段类型问题
-- 解决导入时 patrol_group_id 超出范围的问题

-- 检查表是否存在
SELECT COUNT(*) FROM information_schema.tables
WHERE table_schema = DATABASE() AND table_name = 'floodcontrol_patrol_group';

-- 如果表已存在，需要先修改字段类型
-- 注意：在修改主键字段类型前，需要先删除AUTO_INCREMENT属性，然后重新添加
ALTER TABLE floodcontrol_patrol_group MODIFY COLUMN patrol_group_id BIGINT;
ALTER TABLE floodcontrol_patrol_group MODIFY COLUMN patrol_group_id BIGINT AUTO_INCREMENT;
ALTER TABLE floodcontrol_patrol_group MODIFY COLUMN create_dept BIGINT;
ALTER TABLE floodcontrol_patrol_group MODIFY COLUMN create_by BIGINT;
ALTER TABLE floodcontrol_patrol_group MODIFY COLUMN update_by BIGINT;

-- 如果需要重新创建表，请使用以下语句
/*
DROP TABLE IF EXISTS floodcontrol_patrol_group;
CREATE TABLE floodcontrol_patrol_group (
    patrol_group_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '出巡分组ID',
    segment_id VARCHAR(20) COMMENT '工务段id',
    segment_name VARCHAR(50) COMMENT '工务段名称',
    line_id VARCHAR(20) COMMENT '线路id',
    line_name VARCHAR(50) COMMENT '线路名称',
    station VARCHAR(50) COMMENT '站名',
    rain_gauge_point VARCHAR(50) COMMENT '雨量计点名称',
    rain_gauge_mileage DECIMAL(12,6) COMMENT '雨量计安装里程',
    alarm_range VARCHAR(100) COMMENT '报警影响范围',
    responsible_workshop_id VARCHAR(20) COMMENT '责任车间ID',
    responsible_workshop_name VARCHAR(50) COMMENT '责任车间名称',
    responsible_gang_id VARCHAR(20) COMMENT '责任工区ID',
    responsible_gang_name VARCHAR(50) COMMENT '责任工区名称',
    key_locations TEXT COMMENT '防洪重点地点',
    weak_sections TEXT COMMENT '防洪薄弱地段',
    patrol_group_name VARCHAR(50) COMMENT '巡查分组名称',
    start_mileage DECIMAL(12,6) COMMENT '开始里程',
    end_mileage DECIMAL(12,6) COMMENT '结束里程',
    fence_gate_in VARCHAR(50) COMMENT '进入栅栏门位置',
    fence_gate_out VARCHAR(50) COMMENT '离开栅栏门位置',
    patrol_length DECIMAL(12,6) COMMENT '巡查长度(公里)',
    one_way_time INT COMMENT '单程用时(分钟)',
    gang_name VARCHAR(50) COMMENT '工区名称',
    create_dept BIGINT COMMENT '创建部门',
    create_by BIGINT COMMENT '创建人',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by BIGINT COMMENT '修改人',
    update_time TIMESTAMP COMMENT '修改时间'
) COMMENT = '出巡分组表';
*/
