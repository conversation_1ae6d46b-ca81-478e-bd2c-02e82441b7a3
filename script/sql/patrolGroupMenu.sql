-- 菜单 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1969948587136790529, '出巡分组', '1968865592301887490', '1', 'patrolGroup', 'floodcontrol/patrolGroup/index', 1, 0, 'C', '0', '0', 'floodcontrol:patrolGroup:list', '#', 103, 1, sysdate(), null, null, '出巡分组菜单');

-- 按钮 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1969948587136790530, '出巡分组查询', 1969948587136790529, '1',  '#', '', 1, 0, 'F', '0', '0', 'floodcontrol:patrolGroup:query',        '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1969948587136790531, '出巡分组新增', 1969948587136790529, '2',  '#', '', 1, 0, 'F', '0', '0', 'floodcontrol:patrolGroup:add',          '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1969948587136790532, '出巡分组修改', 1969948587136790529, '3',  '#', '', 1, 0, 'F', '0', '0', 'floodcontrol:patrolGroup:edit',         '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1969948587136790533, '出巡分组删除', 1969948587136790529, '4',  '#', '', 1, 0, 'F', '0', '0', 'floodcontrol:patrolGroup:remove',       '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1969948587136790534, '出巡分组导出', 1969948587136790529, '5',  '#', '', 1, 0, 'F', '0', '0', 'floodcontrol:patrolGroup:export',       '#', 103, 1, sysdate(), null, null, '');
