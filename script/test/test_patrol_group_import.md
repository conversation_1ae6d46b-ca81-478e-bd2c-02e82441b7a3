# 出巡分组导入功能测试指南

## 问题描述
导入时出现错误：`Data truncation: Out of range value for column 'patrol_group_id' at row 1`

## 问题原因
1. 数据库表中 `patrol_group_id` 字段定义为 `INTEGER`，范围有限
2. MyBatis-Plus可能生成超出INTEGER范围的ID值
3. 实体类使用 `IdType.AUTO` 但全局配置为 `ASSIGN_ID`

## 解决方案

### 1. 数据库表结构修复
执行以下SQL修复表结构：

```sql
-- 修改主键字段类型为BIGINT
ALTER TABLE floodcontrol_patrol_group MODIFY COLUMN patrol_group_id BIGINT AUTO_INCREMENT;
ALTER TABLE floodcontrol_patrol_group MODIFY COLUMN create_dept BIGINT;
ALTER TABLE floodcontrol_patrol_group MODIFY COLUMN create_by BIGINT;
ALTER TABLE floodcontrol_patrol_group MODIFY COLUMN update_by BIGINT;
```

### 2. 验证表结构
```sql
DESCRIBE floodcontrol_patrol_group;
```

预期结果：
- `patrol_group_id`: BIGINT, AUTO_INCREMENT
- `create_dept`: BIGINT
- `create_by`: BIGINT  
- `update_by`: BIGINT

### 3. 测试导入功能

#### 准备测试数据
创建Excel文件，包含以下列：
- 序号
- 线路名称（必填）
- 车站名称
- 工务段名称
- 雨量计点
- 雨量计安装里程
- 报警影响范围
- 责任车间
- 责任工区
- 巡查分组名称（必填）
- 开始里程
- 结束里程
- 进入栅栏门位置
- 离开栅栏门位置
- 巡查长度(公里)
- 单程用时(分钟)
- 工区名称
- 防洪重点地点
- 防洪薄弱地段

#### 测试用例

**测试用例1：基本导入**
```
序号: 1
线路名称: 京沪线
车站名称: 北京站
工务段名称: 北京工务段
巡查分组名称: 北京站巡查组1
开始里程: 100
结束里程: 105
```

**测试用例2：重复数据测试**
- 导入相同的巡查分组名称
- 测试updateSupport=false（应该跳过）
- 测试updateSupport=true（应该更新）

**测试用例3：数据验证测试**
- 空的线路名称（应该失败）
- 空的巡查分组名称（应该失败）
- 开始里程大于结束里程（应该失败）

### 4. API测试

#### 导入接口测试
```bash
curl -X POST \
  http://localhost:8080/floodcontrol/patrolGroup/importData \
  -H 'Content-Type: multipart/form-data' \
  -F 'file=@test_data.xlsx' \
  -F 'updateSupport=false'
```

#### 模板下载测试
```bash
curl -X POST \
  http://localhost:8080/floodcontrol/patrolGroup/importTemplate \
  -o template.xlsx
```

### 5. 预期结果

#### 成功导入
```json
{
    "code": 200,
    "msg": "恭喜您，数据已全部导入成功！共 2 条，数据如下：<br/>1、巡查分组 北京站巡查组1 导入成功<br/>2、巡查分组 天津站巡查组1 导入成功",
    "data": null
}
```

#### 验证失败
```json
{
    "code": 500,
    "msg": "很抱歉，导入失败！共 1 条数据格式不正确，错误如下：<br/>1、第2行：线路名称不能为空",
    "data": null
}
```

### 6. 故障排除

如果仍然出现ID范围错误：
1. 检查数据库表结构是否正确修改
2. 重启应用服务器
3. 清除MyBatis缓存
4. 检查实体类的@TableId注解配置

### 7. 监控和日志

查看应用日志中的SQL语句：
```
INSERT INTO floodcontrol_patrol_group (segment_name, line_name, ...) VALUES (?, ?, ...)
```

注意：INSERT语句中不应该包含 `patrol_group_id` 字段，让数据库自动生成。
