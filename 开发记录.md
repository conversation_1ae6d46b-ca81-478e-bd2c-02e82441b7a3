### 1、合并代码

```cmd
git fetch # 更新所有分支
git checkout master   #切换到master分支
git pull    #拉取最新的代码
git checkout xxx(自己命名的分支)

# 主要要解决冲突后合并
git merge master    #合并分支
git push origin xxx    #本地分支关联到远程分支上

git add .  # 添加当前目录下的所有更改

git commit -m "提交信息"

git branch # 查看分支信息
```

### 2、前后端加密

前后端要同步开启或关闭加密功能

![image-20250826155824829](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250826155824829.png)

![image-20250826155920620](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250826155920620.png)
